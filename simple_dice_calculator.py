import os
import numpy as np
import nibabel as nib
from PIL import Image
import pandas as pd
from pathlib import Path


def compute_segmentation_metrics(mask_gt, mask_pred):
    """
    计算分割评估指标

    参数:
        mask_gt: 真实标签掩码 (布尔数组)
        mask_pred: 预测结果掩码 (布尔数组)

    返回:
        dict: 包含各种评估指标的字典
    """
    # 计算混淆矩阵的各个值
    tp = np.sum(mask_gt & mask_pred)  # True Positive
    fp = np.sum(~mask_gt & mask_pred)  # False Positive
    fn = np.sum(mask_gt & ~mask_pred)  # False Negative
    tn = np.sum(~mask_gt & ~mask_pred)  # True Negative

    # 计算各种指标
    metrics = {}

    # Dice系数 (F1-score)
    if tp + fp + fn == 0:
        metrics['dice'] = np.NaN
    else:
        metrics['dice'] = 2 * tp / (2 * tp + fp + fn)

    # IoU (Intersection over Union) / Jaccard Index
    if tp + fp + fn == 0:
        metrics['iou'] = np.NaN
    else:
        metrics['iou'] = tp / (tp + fp + fn)

    # Sensitivity (Recall / True Positive Rate)
    if tp + fn == 0:
        metrics['sensitivity'] = np.NaN
    else:
        metrics['sensitivity'] = tp / (tp + fn)

    # PPV (Precision / Positive Predictive Value)
    if tp + fp == 0:
        metrics['ppv'] = np.NaN
    else:
        metrics['ppv'] = tp / (tp + fp)

    # Specificity (True Negative Rate)
    if tn + fp == 0:
        metrics['specificity'] = np.NaN
    else:
        metrics['specificity'] = tn / (tn + fp)

    # Accuracy
    total = tp + fp + fn + tn
    if total == 0:
        metrics['accuracy'] = np.NaN
    else:
        metrics['accuracy'] = (tp + tn) / total

    # F1-score (same as Dice for binary segmentation)
    metrics['f1_score'] = metrics['dice']

    # 添加混淆矩阵信息
    metrics['tp'] = int(tp)
    metrics['fp'] = int(fp)
    metrics['fn'] = int(fn)
    metrics['tn'] = int(tn)

    return metrics


def compute_dice_coefficient(mask_gt, mask_pred):
    """计算Dice系数（保持向后兼容）"""
    metrics = compute_segmentation_metrics(mask_gt, mask_pred)
    return metrics['dice']


def load_image_as_mask(file_path):
    """加载图像并转换为布尔掩码"""
    file_path = Path(file_path)
    
    try:
        if str(file_path).endswith('.nii.gz') or file_path.suffix == '.nii':
            # NIfTI格式
            nii_image = nib.load(str(file_path))
            image_data = nii_image.get_fdata()
            return (image_data > 0).astype(bool)
        else:
            # 常见图像格式
            image = Image.open(file_path)
            if image.mode != 'L':
                image = image.convert('L')
            image_array = np.array(image)
            return (image_array > 0).astype(bool)
    except Exception as e:
        print(f"加载图像失败 {file_path}: {e}")
        return None


def find_matching_files(folder1, folder2):
    """找到两个文件夹中的同名文件"""
    folder1_path = Path(folder1)
    folder2_path = Path(folder2)
    
    # 获取文件名映射（不包含扩展名）
    files1 = {}
    for file_path in folder1_path.iterdir():
        if file_path.is_file():
            if str(file_path).endswith('.nii.gz'):
                base_name = file_path.name[:-7]
            else:
                base_name = file_path.stem
            files1[base_name] = file_path
    
    # 找到匹配的文件对
    matching_pairs = []
    for file_path in folder2_path.iterdir():
        if file_path.is_file():
            if str(file_path).endswith('.nii.gz'):
                base_name = file_path.name[:-7]
            else:
                base_name = file_path.stem
            
            if base_name in files1:
                matching_pairs.append((files1[base_name], file_path, base_name))
    
    return matching_pairs


def calculate_segmentation_metrics(folder1, folder2, output_file="segmentation_results.csv"):
    """
    计算两个文件夹中同名图片的分割评估指标

    参数:
        folder1: 第一个文件夹路径（通常是真实标签）
        folder2: 第二个文件夹路径（通常是预测结果）
        output_file: 输出CSV文件名
    """
    print(f"正在比较文件夹:")
    print(f"  文件夹1: {folder1}")
    print(f"  文件夹2: {folder2}")
    
    # 检查文件夹是否存在
    if not os.path.exists(folder1):
        print(f"错误: 文件夹不存在 - {folder1}")
        return
    
    if not os.path.exists(folder2):
        print(f"错误: 文件夹不存在 - {folder2}")
        return
    
    # 找到匹配的文件对
    matching_files = find_matching_files(folder1, folder2)
    
    if not matching_files:
        print("没有找到匹配的文件对")
        return
    
    print(f"找到 {len(matching_files)} 对匹配的文件")
    
    results = []
    successful_count = 0
    
    for file1_path, file2_path, filename in matching_files:
        print(f"处理: {filename}")
        
        # 加载图像
        mask1 = load_image_as_mask(file1_path)
        mask2 = load_image_as_mask(file2_path)
        
        if mask1 is None or mask2 is None:
            print(f"  跳过: 图像加载失败")
            continue
        
        # 检查尺寸是否匹配
        if mask1.shape != mask2.shape:
            print(f"  跳过: 图像尺寸不匹配 {mask1.shape} vs {mask2.shape}")
            continue
        
        # 计算所有分割指标
        metrics = compute_segmentation_metrics(mask1, mask2)

        results.append({
            '文件名': filename,
            'Dice系数': metrics['dice'],
            'IoU': metrics['iou'],
            'Sensitivity(Sen)': metrics['sensitivity'],
            'PPV(Precision)': metrics['ppv'],
            'Specificity': metrics['specificity'],
            'Accuracy': metrics['accuracy'],
            'F1_Score': metrics['f1_score'],
            'TP': metrics['tp'],
            'FP': metrics['fp'],
            'FN': metrics['fn'],
            'TN': metrics['tn'],
            '文件1路径': str(file1_path),
            '文件2路径': str(file2_path),
            '图像尺寸': str(mask1.shape),
            '文件1前景像素': int(np.sum(mask1)),
            '文件2前景像素': int(np.sum(mask2))
        })

        print(f"  Dice: {metrics['dice']:.4f}, IoU: {metrics['iou']:.4f}, Sen: {metrics['sensitivity']:.4f}, PPV: {metrics['ppv']:.4f}")
        successful_count += 1
    
    if not results:
        print("没有成功处理任何文件对")
        return
    
    # 创建结果数据框
    df = pd.DataFrame(results)

    if not df.empty:
        # 计算各指标的统计信息
        metrics_cols = ['Dice系数', 'IoU', 'Sensitivity(Sen)', 'PPV(Precision)', 'Specificity', 'Accuracy']

        print(f"\n=== 结果统计 ===")
        print(f"成功处理的文件对: {successful_count}")

        for col in metrics_cols:
            if col in df.columns:
                values = df[col].dropna()
                if len(values) > 0:
                    print(f"\n{col}:")
                    print(f"  平均值: {values.mean():.4f}")
                    print(f"  标准差: {values.std():.4f}")
                    print(f"  最小值: {values.min():.4f}")
                    print(f"  最大值: {values.max():.4f}")
                    print(f"  中位数: {values.median():.4f}")

        # 保存结果
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"\n结果已保存到: {output_file}")

        # 显示前几行结果
        print(f"\n前5个结果:")
        display_cols = ['文件名', 'Dice系数', 'IoU', 'Sensitivity(Sen)', 'PPV(Precision)']
        available_cols = [col for col in display_cols if col in df.columns]
        print(df[available_cols].head().to_string(index=False, float_format='%.4f'))
    
    return df


# 添加向后兼容的函数
def calculate_dice_scores(folder1, folder2, output_file="dice_results.csv"):
    """向后兼容的函数，调用新的分割指标计算函数"""
    return calculate_segmentation_metrics(folder1, folder2, output_file)


# 使用示例
if __name__ == "__main__":
    # 交互式输入
    folder1 = r'/media/ubun/Teacher/hyf/MedSAM/data/FLARE22Train/mask_05/'
    folder2 = r'/media/ubun/Teacher/hyf/MedSAM/data/FLARE22Train/results_05/'
    output_file = r'/media/ubun/Teacher/hyf/MedSAM/data/FLARE22Train/zhibiao_05.csv'
    if not output_file:
        output_file = "segmentation_results.csv"

    # 计算分割指标
    results = calculate_segmentation_metrics(folder1, folder2, output_file)
