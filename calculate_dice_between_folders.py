#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
计算两个文件夹中同名图片的Dice系数
支持PNG、JPG、JPEG、NII.GZ等格式
作者: AI Assistant
日期: 2025-06-15
"""

import os
import sys
import argparse
import csv
import numpy as np
import nibabel as nib
from PIL import Image
import pandas as pd
from pathlib import Path
import logging

# 导入现有的Dice计算函数
from utils.SurfaceDice import compute_dice_coefficient

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def load_image(file_path):
    """
    加载图像文件，支持多种格式
    
    Args:
        file_path (str): 图像文件路径
        
    Returns:
        numpy.ndarray: 图像数据，布尔类型的掩码
    """
    file_path = Path(file_path)
    file_ext = file_path.suffix.lower()
    
    try:
        if file_ext in ['.nii', '.gz'] or str(file_path).endswith('.nii.gz'):
            # 处理NIfTI格式
            nii_image = nib.load(str(file_path))
            image_data = nii_image.get_fdata()
            # 转换为布尔掩码（假设非零值为前景）
            mask = image_data > 0
            return mask.astype(bool)
            
        elif file_ext in ['.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.tif']:
            # 处理常见图像格式
            image = Image.open(file_path)
            # 转换为灰度图像
            if image.mode != 'L':
                image = image.convert('L')
            image_array = np.array(image)
            # 转换为布尔掩码（假设非零值为前景）
            mask = image_array > 0
            return mask.astype(bool)
            
        else:
            logger.warning(f"不支持的文件格式: {file_ext}")
            return None
            
    except Exception as e:
        logger.error(f"加载图像失败 {file_path}: {str(e)}")
        return None


def get_matching_files(folder1, folder2, supported_extensions=None):
    """
    获取两个文件夹中同名的文件对
    
    Args:
        folder1 (str): 第一个文件夹路径
        folder2 (str): 第二个文件夹路径
        supported_extensions (list): 支持的文件扩展名列表
        
    Returns:
        list: 包含匹配文件对的列表 [(file1_path, file2_path, filename), ...]
    """
    if supported_extensions is None:
        supported_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.tif', '.nii', '.gz']
    
    folder1_path = Path(folder1)
    folder2_path = Path(folder2)
    
    if not folder1_path.exists():
        logger.error(f"文件夹不存在: {folder1}")
        return []
    
    if not folder2_path.exists():
        logger.error(f"文件夹不存在: {folder2}")
        return []
    
    # 获取第一个文件夹中的所有文件
    files1 = {}
    for file_path in folder1_path.iterdir():
        if file_path.is_file():
            # 处理.nii.gz文件的特殊情况
            if str(file_path).endswith('.nii.gz'):
                base_name = file_path.name[:-7]  # 移除.nii.gz
                files1[base_name] = file_path
            else:
                base_name = file_path.stem  # 不包含扩展名的文件名
                if file_path.suffix.lower() in supported_extensions:
                    files1[base_name] = file_path
    
    # 获取第二个文件夹中的匹配文件
    matching_files = []
    for file_path in folder2_path.iterdir():
        if file_path.is_file():
            # 处理.nii.gz文件的特殊情况
            if str(file_path).endswith('.nii.gz'):
                base_name = file_path.name[:-7]  # 移除.nii.gz
            else:
                base_name = file_path.stem  # 不包含扩展名的文件名
                if file_path.suffix.lower() not in supported_extensions:
                    continue
            
            if base_name in files1:
                matching_files.append((files1[base_name], file_path, base_name))
    
    logger.info(f"找到 {len(matching_files)} 对匹配的文件")
    return matching_files


def calculate_dice_for_folders(folder1, folder2, output_csv=None, threshold=0.5):
    """
    计算两个文件夹中同名图片的Dice系数
    
    Args:
        folder1 (str): 第一个文件夹路径（真实标签）
        folder2 (str): 第二个文件夹路径（预测结果）
        output_csv (str): 输出CSV文件路径
        threshold (float): 二值化阈值
        
    Returns:
        pandas.DataFrame: 包含结果的数据框
    """
    # 获取匹配的文件对
    matching_files = get_matching_files(folder1, folder2)
    
    if not matching_files:
        logger.warning("没有找到匹配的文件对")
        return pd.DataFrame()
    
    results = []
    
    for file1_path, file2_path, filename in matching_files:
        logger.info(f"处理文件对: {filename}")
        
        # 加载图像
        mask1 = load_image(file1_path)
        mask2 = load_image(file2_path)
        
        if mask1 is None or mask2 is None:
            logger.warning(f"跳过文件对 {filename}: 图像加载失败")
            continue
        
        # 确保两个掩码的形状相同
        if mask1.shape != mask2.shape:
            logger.warning(f"跳过文件对 {filename}: 图像尺寸不匹配 {mask1.shape} vs {mask2.shape}")
            continue
        
        # 计算Dice系数
        try:
            dice_score = compute_dice_coefficient(mask1, mask2)
            
            results.append({
                'filename': filename,
                'file1_path': str(file1_path),
                'file2_path': str(file2_path),
                'dice_score': dice_score,
                'mask1_shape': str(mask1.shape),
                'mask2_shape': str(mask2.shape),
                'mask1_positive_pixels': int(np.sum(mask1)),
                'mask2_positive_pixels': int(np.sum(mask2))
            })
            
            logger.info(f"  Dice系数: {dice_score:.4f}")
            
        except Exception as e:
            logger.error(f"计算Dice系数失败 {filename}: {str(e)}")
            continue
    
    # 创建结果数据框
    df_results = pd.DataFrame(results)
    
    if not df_results.empty:
        # 计算统计信息
        mean_dice = df_results['dice_score'].mean()
        std_dice = df_results['dice_score'].std()
        min_dice = df_results['dice_score'].min()
        max_dice = df_results['dice_score'].max()
        
        logger.info(f"\n=== 统计结果 ===")
        logger.info(f"处理的文件对数量: {len(df_results)}")
        logger.info(f"平均Dice系数: {mean_dice:.4f}")
        logger.info(f"标准差: {std_dice:.4f}")
        logger.info(f"最小值: {min_dice:.4f}")
        logger.info(f"最大值: {max_dice:.4f}")
        
        # 保存结果到CSV文件
        if output_csv:
            df_results.to_csv(output_csv, index=False, encoding='utf-8-sig')
            logger.info(f"结果已保存到: {output_csv}")
    
    return df_results


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='计算两个文件夹中同名图片的Dice系数')
    parser.add_argument('folder1', help='第一个文件夹路径（真实标签）')
    parser.add_argument('folder2', help='第二个文件夹路径（预测结果）')
    parser.add_argument('--output', '-o', default='dice_results.csv', help='输出CSV文件路径')
    parser.add_argument('--threshold', '-t', type=float, default=0.5, help='二值化阈值')
    
    args = parser.parse_args()
    
    # 检查输入文件夹
    if not os.path.exists(args.folder1):
        logger.error(f"文件夹不存在: {args.folder1}")
        sys.exit(1)
    
    if not os.path.exists(args.folder2):
        logger.error(f"文件夹不存在: {args.folder2}")
        sys.exit(1)
    
    # 计算Dice系数
    results = calculate_dice_for_folders(
        args.folder1, 
        args.folder2, 
        args.output, 
        args.threshold
    )
    
    if results.empty:
        logger.warning("没有成功处理任何文件对")
    else:
        logger.info("处理完成！")


if __name__ == "__main__":
    main()
