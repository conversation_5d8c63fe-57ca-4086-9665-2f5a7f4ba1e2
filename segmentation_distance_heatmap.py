#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分割结果距离变换热力图生成器
专门针对分割结果生成距离变换热力图，突出显示分割区域
"""

import os
import numpy as np
import matplotlib.pyplot as plt
import cv2
from skimage import io
from datetime import datetime

# 配置参数 - 根据用户偏好直接在代码中定义路径
IMAGE_PATH = "/media/ubun/Teacher/hyf/MedSAM/data/FLARE22Train/images_png/FLARE22_Tr_0033_0000/024.png"                    # 原始图像路径
SEGMENTATION_PATH = "/media/ubun/Teacher/hyf/MedSAM/data/FLARE22Train/mask_33/024.png"  # 分割结果路径模式
OUTPUT_DIR = "/media/ubun/Teacher/hyf/MedSAM/data/FLARE22Train/results_heatmap/"                # 输出目录

def load_image(image_path):
    """加载原始图像"""
    print(f"📖 加载原始图像: {image_path}")
    
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"图像文件不存在: {image_path}")
    
    img = io.imread(image_path)
    print(f"   图像形状: {img.shape}")
    
    return img

def load_segmentation_mask(seg_path):
    """加载分割结果"""
    print(f"📖 加载分割结果: {seg_path}")
    
    if not os.path.exists(seg_path):
        raise FileNotFoundError(f"分割结果文件不存在: {seg_path}")
    
    # 加载分割掩码
    seg_mask = io.imread(seg_path)
    
    # 转换为二值图像
    if len(seg_mask.shape) == 3:
        seg_mask = cv2.cvtColor(seg_mask, cv2.COLOR_RGB2GRAY)
    
    # 确保是二值图像 (0 和 255)
    _, seg_binary = cv2.threshold(seg_mask, 127, 255, cv2.THRESH_BINARY)
    
    print(f"   分割掩码形状: {seg_binary.shape}")
    print(f"   分割像素数: {np.sum(seg_binary > 0):,}")
    
    return seg_binary

def generate_distance_transform_heatmap(seg_binary, mode='both'):
    """
    生成距离变换热力图
    
    参数:
        seg_binary: 二值分割掩码
        mode: 'inside' - 只计算分割区域内部的距离
              'outside' - 只计算分割区域外部的距离  
              'both' - 计算内部和外部距离
    """
    print(f"🔥 生成距离变换热力图 (模式: {mode})...")
    
    if mode == 'inside':
        # 计算分割区域内部到边界的距离
        dist_inside = cv2.distanceTransform(seg_binary, cv2.DIST_L2, 5)
        return dist_inside
        
    elif mode == 'outside':
        # 计算分割区域外部到边界的距离
        seg_inverted = cv2.bitwise_not(seg_binary)
        dist_outside = cv2.distanceTransform(seg_inverted, cv2.DIST_L2, 5)
        return dist_outside
        
    else:  # mode == 'both'
        # 计算内部和外部距离，然后组合
        dist_inside = cv2.distanceTransform(seg_binary, cv2.DIST_L2, 5)
        seg_inverted = cv2.bitwise_not(seg_binary)
        dist_outside = cv2.distanceTransform(seg_inverted, cv2.DIST_L2, 5)
        
        # 组合：内部为正值，外部为负值
        combined_dist = np.where(seg_binary > 0, dist_inside, -dist_outside)
        
        return combined_dist, dist_inside, dist_outside

def create_distance_heatmap_visualization(img, seg_binary, distance_maps, output_dir):
    """创建距离变换热力图可视化"""
    print("🎨 创建距离变换热力图可视化...")
    
    os.makedirs(output_dir, exist_ok=True)
    
    # 根据distance_maps的类型确定布局
    if isinstance(distance_maps, tuple):
        # 包含三个距离图：组合、内部、外部
        combined_dist, dist_inside, dist_outside = distance_maps
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    else:
        # 只有一个距离图
        fig, axes = plt.subplots(2, 2, figsize=(12, 12))
    
    fig.suptitle('分割结果距离变换热力图分析', fontsize=16, fontweight='bold')
    
    # 1. 原始图像
    axes[0, 0].imshow(img, cmap='gray' if len(img.shape) == 2 else None)
    axes[0, 0].set_title("原始图像", fontsize=14, fontweight='bold')
    axes[0, 0].axis('off')
    
    # 2. 分割结果
    axes[0, 1].imshow(img, cmap='gray' if len(img.shape) == 2 else None)
    # 叠加分割掩码
    seg_colored = np.zeros((*seg_binary.shape, 4))
    seg_colored[seg_binary > 0] = [1, 0, 0, 0.6]  # 红色半透明
    axes[0, 1].imshow(seg_colored)
    axes[0, 1].set_title("分割结果叠加", fontsize=14, fontweight='bold')
    axes[0, 1].axis('off')
    
    if isinstance(distance_maps, tuple):
        # 显示三种距离图
        combined_dist, dist_inside, dist_outside = distance_maps
        
        # 3. 组合距离图
        im1 = axes[0, 2].imshow(combined_dist, cmap='RdBu_r', interpolation='bilinear')
        axes[0, 2].set_title("组合距离变换\n(内部正值，外部负值)", fontsize=12, fontweight='bold')
        axes[0, 2].axis('off')
        plt.colorbar(im1, ax=axes[0, 2], shrink=0.8, label='距离')
        
        # 4. 内部距离图
        im2 = axes[1, 0].imshow(dist_inside, cmap='hot', interpolation='bilinear')
        axes[1, 0].set_title("分割区域内部距离", fontsize=12, fontweight='bold')
        axes[1, 0].axis('off')
        plt.colorbar(im2, ax=axes[1, 0], shrink=0.8, label='距离')
        
        # 5. 外部距离图
        im3 = axes[1, 1].imshow(dist_outside, cmap='viridis', interpolation='bilinear')
        axes[1, 1].set_title("分割区域外部距离", fontsize=12, fontweight='bold')
        axes[1, 1].axis('off')
        plt.colorbar(im3, ax=axes[1, 1], shrink=0.8, label='距离')
        
        # 6. 重点区域热力图叠加
        # 只在分割区域显示内部距离
        highlighted_heatmap = np.zeros_like(dist_inside)
        highlighted_heatmap[seg_binary > 0] = dist_inside[seg_binary > 0]
        
        axes[1, 2].imshow(img, cmap='gray' if len(img.shape) == 2 else None, alpha=0.7)
        im4 = axes[1, 2].imshow(highlighted_heatmap, cmap='jet', alpha=0.8, interpolation='bilinear')
        axes[1, 2].set_title("重点区域热力图叠加", fontsize=12, fontweight='bold')
        axes[1, 2].axis('off')
        plt.colorbar(im4, ax=axes[1, 2], shrink=0.8, label='分割区域距离')
        
    else:
        # 只显示单一距离图
        distance_map = distance_maps
        
        # 3. 距离变换热力图
        im1 = axes[1, 0].imshow(distance_map, cmap='hot', interpolation='bilinear')
        axes[1, 0].set_title("距离变换热力图", fontsize=14, fontweight='bold')
        axes[1, 0].axis('off')
        plt.colorbar(im1, ax=axes[1, 0], shrink=0.8, label='距离')
        
        # 4. 重点区域热力图叠加
        axes[1, 1].imshow(img, cmap='gray' if len(img.shape) == 2 else None, alpha=0.7)
        im2 = axes[1, 1].imshow(distance_map, cmap='jet', alpha=0.8, interpolation='bilinear')
        axes[1, 1].set_title("热力图叠加", fontsize=14, fontweight='bold')
        axes[1, 1].axis('off')
        plt.colorbar(im2, ax=axes[1, 1], shrink=0.8, label='距离')
    
    plt.tight_layout()
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    viz_path = os.path.join(output_dir, f"distance_heatmap_visualization_{timestamp}.png")
    plt.savefig(viz_path, dpi=300, bbox_inches='tight')
    print(f"   可视化结果已保存: {viz_path}")
    
    # 显示结果
    plt.show()
    
    return viz_path

def save_distance_heatmap_data(distance_maps, output_dir):
    """保存距离变换数据"""
    print("💾 保存距离变换数据...")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    saved_paths = []
    
    if isinstance(distance_maps, tuple):
        combined_dist, dist_inside, dist_outside = distance_maps
        
        # 保存组合距离图
        combined_path = os.path.join(output_dir, f"combined_distance_{timestamp}.npy")
        np.save(combined_path, combined_dist)
        saved_paths.append(combined_path)
        
        # 保存内部距离图
        inside_path = os.path.join(output_dir, f"inside_distance_{timestamp}.npy")
        np.save(inside_path, dist_inside)
        saved_paths.append(inside_path)
        
        # 保存外部距离图
        outside_path = os.path.join(output_dir, f"outside_distance_{timestamp}.npy")
        np.save(outside_path, dist_outside)
        saved_paths.append(outside_path)
        
    else:
        # 保存单一距离图
        dist_path = os.path.join(output_dir, f"distance_transform_{timestamp}.npy")
        np.save(dist_path, distance_maps)
        saved_paths.append(dist_path)
    
    for path in saved_paths:
        print(f"   数据已保存: {path}")
    
    return saved_paths

def print_distance_statistics(distance_maps, seg_binary):
    """打印距离变换统计信息"""
    print("\n📊 距离变换统计信息:")
    
    if isinstance(distance_maps, tuple):
        combined_dist, dist_inside, dist_outside = distance_maps
        
        print(f"\n组合距离变换:")
        print(f"   数值范围: [{combined_dist.min():.3f}, {combined_dist.max():.3f}]")
        print(f"   平均值: {combined_dist.mean():.3f}")
        print(f"   标准差: {combined_dist.std():.3f}")
        
        print(f"\n内部距离变换:")
        print(f"   最大距离: {dist_inside.max():.3f}")
        print(f"   平均距离: {dist_inside[seg_binary > 0].mean():.3f}")
        print(f"   分割区域中心到边界的最大距离: {dist_inside.max():.3f}")
        
        print(f"\n外部距离变换:")
        print(f"   最大距离: {dist_outside.max():.3f}")
        print(f"   平均距离: {dist_outside[seg_binary == 0].mean():.3f}")
        
    else:
        distance_map = distance_maps
        print(f"\n距离变换:")
        print(f"   数值范围: [{distance_map.min():.3f}, {distance_map.max():.3f}]")
        print(f"   平均值: {distance_map.mean():.3f}")
        print(f"   标准差: {distance_map.std():.3f}")
        print(f"   最大距离: {distance_map.max():.3f}")

def find_segmentation_file():
    """查找分割结果文件"""
    import glob
    
    # 尝试不同的可能路径
    possible_paths = [
        "heat_map_results/segmentation_*.png",
        "segmentation_result.png",
        "*.png"
    ]
    
    for pattern in possible_paths:
        files = glob.glob(pattern)
        if files:
            # 返回最新的文件
            latest_file = max(files, key=os.path.getctime)
            print(f"找到分割结果文件: {latest_file}")
            return latest_file
    
    return None

def main():
    """主函数"""
    print("=" * 60)
    print("🔥 分割结果距离变换热力图生成器")
    print("=" * 60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 加载原始图像
        img = load_image(IMAGE_PATH)
        
        # 2. 查找并加载分割结果
        seg_file = find_segmentation_file()
        if seg_file is None:
            print("❌ 未找到分割结果文件，请确保已生成分割结果")
            print("提示：可以先运行 heat_map.py 生成分割结果")
            return
        
        seg_binary = load_segmentation_mask(seg_file)
        
        # 3. 生成距离变换热力图（包含内部、外部和组合）
        distance_maps = generate_distance_transform_heatmap(seg_binary, mode='both')
        
        # 4. 打印统计信息
        print_distance_statistics(distance_maps, seg_binary)
        
        # 5. 创建可视化
        viz_path = create_distance_heatmap_visualization(
            img, seg_binary, distance_maps, OUTPUT_DIR
        )
        
        # 6. 保存数据
        saved_paths = save_distance_heatmap_data(distance_maps, OUTPUT_DIR)
        
        print("\n🎉 距离变换热力图生成完成!")
        print(f"输出目录: {OUTPUT_DIR}")
        print(f"可视化结果: {viz_path}")
        print("重点展示了分割区域的距离变换特征")
        
    except Exception as e:
        print(f"\n❌ 错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
