#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版分割评估器
计算Dice、IoU、Sensitivity、PPV等多种分割评估指标
支持批量处理和详细统计分析
"""

import os
import numpy as np
import nibabel as nib
from PIL import Image
import pandas as pd
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime


def compute_comprehensive_metrics(mask_gt, mask_pred):
    """
    计算全面的分割评估指标
    
    参数:
        mask_gt: 真实标签掩码 (布尔数组)
        mask_pred: 预测结果掩码 (布尔数组)
    
    返回:
        dict: 包含所有评估指标的字典
    """
    # 确保输入是布尔类型
    mask_gt = mask_gt.astype(bool)
    mask_pred = mask_pred.astype(bool)
    
    # 计算混淆矩阵
    tp = np.sum(mask_gt & mask_pred)  # True Positive
    fp = np.sum(~mask_gt & mask_pred)  # False Positive
    fn = np.sum(mask_gt & ~mask_pred)  # False Negative
    tn = np.sum(~mask_gt & ~mask_pred)  # True Negative
    
    metrics = {}
    
    # 基础混淆矩阵指标
    metrics['TP'] = int(tp)
    metrics['FP'] = int(fp)
    metrics['FN'] = int(fn)
    metrics['TN'] = int(tn)
    
    # Dice系数 (Sørensen-Dice coefficient)
    if tp + fp + fn == 0:
        metrics['Dice'] = 1.0 if tp == 0 else np.NaN
    else:
        metrics['Dice'] = 2 * tp / (2 * tp + fp + fn)
    
    # IoU (Intersection over Union) / Jaccard Index
    if tp + fp + fn == 0:
        metrics['IoU'] = 1.0 if tp == 0 else np.NaN
    else:
        metrics['IoU'] = tp / (tp + fp + fn)
    
    # Sensitivity (Recall / True Positive Rate)
    if tp + fn == 0:
        metrics['Sensitivity'] = np.NaN
    else:
        metrics['Sensitivity'] = tp / (tp + fn)
    
    # PPV (Precision / Positive Predictive Value)
    if tp + fp == 0:
        metrics['PPV'] = np.NaN
    else:
        metrics['PPV'] = tp / (tp + fp)
    
    # Specificity (True Negative Rate)
    if tn + fp == 0:
        metrics['Specificity'] = np.NaN
    else:
        metrics['Specificity'] = tn / (tn + fp)
    
    # NPV (Negative Predictive Value)
    if tn + fn == 0:
        metrics['NPV'] = np.NaN
    else:
        metrics['NPV'] = tn / (tn + fn)
    
    # Accuracy
    total = tp + fp + fn + tn
    if total == 0:
        metrics['Accuracy'] = np.NaN
    else:
        metrics['Accuracy'] = (tp + tn) / total
    
    # F1-score (same as Dice for binary segmentation)
    metrics['F1_Score'] = metrics['Dice']
    
    # Balanced Accuracy
    if (tp + fn == 0) or (tn + fp == 0):
        metrics['Balanced_Accuracy'] = np.NaN
    else:
        sensitivity = tp / (tp + fn)
        specificity = tn / (tn + fp)
        metrics['Balanced_Accuracy'] = (sensitivity + specificity) / 2
    
    # Matthews Correlation Coefficient (MCC)
    denominator = np.sqrt((tp + fp) * (tp + fn) * (tn + fp) * (tn + fn))
    if denominator == 0:
        metrics['MCC'] = 0.0
    else:
        metrics['MCC'] = (tp * tn - fp * fn) / denominator
    
    # Volume similarity
    vol_gt = tp + fn
    vol_pred = tp + fp
    if vol_gt + vol_pred == 0:
        metrics['Volume_Similarity'] = 1.0
    else:
        metrics['Volume_Similarity'] = 1 - abs(vol_gt - vol_pred) / (vol_gt + vol_pred)
    
    return metrics


def load_image_as_mask(file_path):
    """加载图像并转换为布尔掩码"""
    file_path = Path(file_path)
    
    try:
        if str(file_path).endswith('.nii.gz') or file_path.suffix == '.nii':
            # NIfTI格式
            nii_image = nib.load(str(file_path))
            image_data = nii_image.get_fdata()
            return (image_data > 0).astype(bool)
        else:
            # 常见图像格式
            image = Image.open(file_path)
            if image.mode != 'L':
                image = image.convert('L')
            image_array = np.array(image)
            return (image_array > 0).astype(bool)
    except Exception as e:
        print(f"加载图像失败 {file_path}: {e}")
        return None


def find_matching_files(folder1, folder2):
    """找到两个文件夹中的同名文件"""
    folder1_path = Path(folder1)
    folder2_path = Path(folder2)
    
    # 获取文件名映射（不包含扩展名）
    files1 = {}
    for file_path in folder1_path.iterdir():
        if file_path.is_file():
            if str(file_path).endswith('.nii.gz'):
                base_name = file_path.name[:-7]
            else:
                base_name = file_path.stem
            files1[base_name] = file_path
    
    # 找到匹配的文件对
    matching_pairs = []
    for file_path in folder2_path.iterdir():
        if file_path.is_file():
            if str(file_path).endswith('.nii.gz'):
                base_name = file_path.name[:-7]
            else:
                base_name = file_path.stem
            
            if base_name in files1:
                matching_pairs.append((files1[base_name], file_path, base_name))
    
    return matching_pairs


def evaluate_segmentation(folder1, folder2, output_file="segmentation_evaluation.csv", 
                         create_plots=True, plot_dir="evaluation_plots"):
    """
    全面评估分割结果
    
    参数:
        folder1: 真实标签文件夹
        folder2: 预测结果文件夹
        output_file: 输出CSV文件
        create_plots: 是否创建可视化图表
        plot_dir: 图表保存目录
    """
    print(f"=== 分割评估开始 ===")
    print(f"真实标签文件夹: {folder1}")
    print(f"预测结果文件夹: {folder2}")
    print(f"评估时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查文件夹
    if not os.path.exists(folder1):
        print(f"错误: 文件夹不存在 - {folder1}")
        return None
    
    if not os.path.exists(folder2):
        print(f"错误: 文件夹不存在 - {folder2}")
        return None
    
    # 找到匹配的文件对
    matching_files = find_matching_files(folder1, folder2)
    
    if not matching_files:
        print("没有找到匹配的文件对")
        return None
    
    print(f"找到 {len(matching_files)} 对匹配的文件")
    
    results = []
    successful_count = 0
    
    for i, (file1_path, file2_path, filename) in enumerate(matching_files, 1):
        print(f"处理 [{i}/{len(matching_files)}]: {filename}")
        
        # 加载图像
        mask_gt = load_image_as_mask(file1_path)
        mask_pred = load_image_as_mask(file2_path)
        
        if mask_gt is None or mask_pred is None:
            print(f"  跳过: 图像加载失败")
            continue
        
        # 检查尺寸
        if mask_gt.shape != mask_pred.shape:
            print(f"  跳过: 图像尺寸不匹配 {mask_gt.shape} vs {mask_pred.shape}")
            continue
        
        # 计算指标
        metrics = compute_comprehensive_metrics(mask_gt, mask_pred)
        
        # 添加文件信息
        result = {
            'Filename': filename,
            'GT_Path': str(file1_path),
            'Pred_Path': str(file2_path),
            'Image_Shape': str(mask_gt.shape),
            'GT_Positive_Pixels': int(np.sum(mask_gt)),
            'Pred_Positive_Pixels': int(np.sum(mask_pred)),
        }
        result.update(metrics)
        
        results.append(result)
        
        # 显示主要指标
        print(f"  Dice: {metrics['Dice']:.4f}, IoU: {metrics['IoU']:.4f}, "
              f"Sen: {metrics['Sensitivity']:.4f}, PPV: {metrics['PPV']:.4f}")
        
        successful_count += 1
    
    if not results:
        print("没有成功处理任何文件对")
        return None
    
    # 创建结果数据框
    df = pd.DataFrame(results)
    
    # 计算统计信息
    metric_columns = ['Dice', 'IoU', 'Sensitivity', 'PPV', 'Specificity', 
                     'NPV', 'Accuracy', 'F1_Score', 'Balanced_Accuracy', 
                     'MCC', 'Volume_Similarity']
    
    print(f"\n=== 评估结果统计 ===")
    print(f"成功处理的文件对: {successful_count}")
    
    # 创建统计摘要
    stats_summary = {}
    for col in metric_columns:
        if col in df.columns:
            values = df[col].dropna()
            if len(values) > 0:
                stats_summary[col] = {
                    'Mean': values.mean(),
                    'Std': values.std(),
                    'Min': values.min(),
                    'Max': values.max(),
                    'Median': values.median(),
                    'Q25': values.quantile(0.25),
                    'Q75': values.quantile(0.75)
                }
                
                print(f"\n{col}:")
                print(f"  平均值: {values.mean():.4f} ± {values.std():.4f}")
                print(f"  范围: [{values.min():.4f}, {values.max():.4f}]")
                print(f"  中位数: {values.median():.4f}")
    
    # 保存详细结果
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"\n详细结果已保存到: {output_file}")
    
    # 保存统计摘要
    stats_file = output_file.replace('.csv', '_statistics.csv')
    stats_df = pd.DataFrame(stats_summary).T
    stats_df.to_csv(stats_file, encoding='utf-8-sig')
    print(f"统计摘要已保存到: {stats_file}")
    
    # 创建可视化图表
    if create_plots and len(df) > 1:
        create_evaluation_plots(df, plot_dir)
    
    return df


def create_evaluation_plots(df, plot_dir="evaluation_plots"):
    """创建评估结果的可视化图表"""
    os.makedirs(plot_dir, exist_ok=True)
    
    metric_columns = ['Dice', 'IoU', 'Sensitivity', 'PPV', 'Specificity', 'Accuracy']
    available_metrics = [col for col in metric_columns if col in df.columns]
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 1. 指标分布箱线图
    if len(available_metrics) > 0:
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        axes = axes.flatten()
        
        for i, metric in enumerate(available_metrics):
            if i < len(axes):
                values = df[metric].dropna()
                if len(values) > 0:
                    axes[i].boxplot(values)
                    axes[i].set_title(f'{metric} Distribution')
                    axes[i].set_ylabel(metric)
                    axes[i].grid(True, alpha=0.3)
        
        # 隐藏多余的子图
        for i in range(len(available_metrics), len(axes)):
            axes[i].set_visible(False)
        
        plt.tight_layout()
        plt.savefig(os.path.join(plot_dir, 'metrics_boxplot.png'), dpi=300, bbox_inches='tight')
        plt.close()
    
    # 2. 指标相关性热力图
    if len(available_metrics) > 1:
        correlation_matrix = df[available_metrics].corr()
        
        plt.figure(figsize=(10, 8))
        sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0,
                   square=True, fmt='.3f')
        plt.title('Metrics Correlation Heatmap')
        plt.tight_layout()
        plt.savefig(os.path.join(plot_dir, 'metrics_correlation.png'), dpi=300, bbox_inches='tight')
        plt.close()
    
    print(f"可视化图表已保存到: {plot_dir}")


# 使用示例
if __name__ == "__main__":
    # 交互式输入
    folder1 = input("请输入真实标签文件夹路径: ").strip().strip('"\'')
    folder2 = input("请输入预测结果文件夹路径: ").strip().strip('"\'')
    
    output_file = input("请输入输出文件名（默认: segmentation_evaluation.csv）: ").strip()
    if not output_file:
        output_file = "segmentation_evaluation.csv"
    
    create_plots = input("是否创建可视化图表？(y/n, 默认: y): ").strip().lower()
    create_plots = create_plots != 'n'
    
    # 执行评估
    results = evaluate_segmentation(folder1, folder2, output_file, create_plots)
