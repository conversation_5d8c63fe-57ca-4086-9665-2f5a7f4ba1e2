import cv2
import numpy as np
import matplotlib.pyplot as plt

# 读取原始图像和分割结果
a='031'
original_image = cv2.imread(f'/media/ubun/Teacher/hyf/MedSAM/data/FLARE22Train/images_png/FLARE22_Tr_0005_0000/{a}.png')
segmentation_result = cv2.imread(f'/media/ubun/Teacher/hyf/MedSAM/data/FLARE22Train/mask_05/{a}.png', cv2.IMREAD_GRAYSCALE)


# 检查图像是否读取成功
if original_image is None or segmentation_result is None:
    raise FileNotFoundError("Check the paths to your image and segmentation files.")

# 将原始图像转换为灰度以应用色彩映射
gray_image = cv2.cvtColor(original_image, cv2.COLOR_BGR2GRAY)

# 生成热力图
heatmap = cv2.applyColorMap(gray_image, cv2.COLORMAP_JET)

# 使用分割结果创建掩码
highlighted_mask = segmentation_result > 0

# 创建一个减弱饱和度的版本的热力图
desaturated_heatmap = cv2.addWeighted(heatmap, 0.5, np.zeros_like(heatmap), 0.5, 0)

# 合并图像，使分割区域保持原始热力图效果，其他区域使用减弱版
final_heatmap = heatmap.copy()
final_heatmap[~highlighted_mask] = desaturated_heatmap[~highlighted_mask]

# 可选：增强分割区域的显示效果
highlighted_heatmap = heatmap.copy()
highlighted_heatmap[highlighted_mask] = cv2.addWeighted(heatmap[highlighted_mask], 0.7, np.full_like(heatmap[highlighted_mask], 255), 0.3, 0)

# 保存最终生成的热力图到本地
cv2.imwrite(f'/media/ubun/Teacher/hyf/MedSAM/data/FLARE22Train/results_heatmap/5_{a}.png', final_heatmap)

# 可视化原始图像、分割结果和最终热力图
plt.figure(figsize=(15, 5))

plt.subplot(1, 3, 1)
plt.title('Original Image')
plt.imshow(cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB))
plt.axis('off')

plt.subplot(1, 3, 2)
plt.title('Segmentation Result')
plt.imshow(segmentation_result, cmap='gray')
plt.axis('off')

plt.subplot(1, 3, 3)
plt.title('Final Heatmap')
plt.imshow(cv2.cvtColor(final_heatmap, cv2.COLOR_BGR2RGB))
plt.axis('off')

plt.show()

