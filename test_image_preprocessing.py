#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试图像预处理功能
验证不同格式的图像是否能正确转换为3通道格式
"""

import numpy as np
import torch
from skimage import io
from PIL import Image
import os


def test_image_preprocessing(image_path):
    """测试图像预处理"""
    print(f"=== 测试图像: {image_path} ===")
    
    if not os.path.exists(image_path):
        print(f"图像文件不存在: {image_path}")
        return False
    
    try:
        # 加载图像
        img_np = io.imread(image_path)
        print(f"原始图像形状: {img_np.shape}")
        print(f"原始图像数据类型: {img_np.dtype}")
        print(f"原始图像值范围: [{img_np.min()}, {img_np.max()}]")
        
        # 转换为3通道
        if len(img_np.shape) == 2:
            img_3c = np.repeat(img_np[:, :, None], 3, axis=-1)
            print("✓ 灰度图像已转换为3通道")
        elif len(img_np.shape) == 3:
            if img_np.shape[2] == 1:
                img_3c = np.repeat(img_np, 3, axis=-1)
                print("✓ 单通道图像已转换为3通道")
            elif img_np.shape[2] == 3:
                img_3c = img_np
                print("✓ RGB图像，直接使用")
            elif img_np.shape[2] == 4:
                img_3c = img_np[:, :, :3]
                print("✓ RGBA图像，已去掉透明度通道")
            else:
                img_3c = img_np[:, :, :3]
                print(f"✓ {img_np.shape[2]}通道图像，已取前3个通道")
        else:
            print(f"✗ 不支持的图像维度: {img_np.shape}")
            return False
        
        # 检查结果
        print(f"转换后形状: {img_3c.shape}")
        print(f"转换后数据类型: {img_3c.dtype}")
        
        # 确保数据类型正确
        if img_3c.dtype != np.uint8:
            if img_3c.max() <= 1.0:
                img_3c = (img_3c * 255).astype(np.uint8)
                print("✓ 已转换数据类型为uint8")
            else:
                img_3c = img_3c.astype(np.uint8)
                print("✓ 已转换数据类型为uint8")
        
        # 创建tensor测试
        img_normalized = (img_3c - img_3c.min()) / np.clip(
            img_3c.max() - img_3c.min(), a_min=1e-8, a_max=None
        )
        
        img_tensor = torch.tensor(img_normalized).float().permute(2, 0, 1).unsqueeze(0)
        
        print(f"最终tensor形状: {img_tensor.shape}")
        
        # 验证tensor形状
        if img_tensor.shape[1] == 3:
            print("✓ 成功创建3通道tensor")
            return True
        else:
            print(f"✗ tensor通道数错误: {img_tensor.shape[1]}")
            return False
            
    except Exception as e:
        print(f"✗ 预处理失败: {e}")
        return False


def create_test_images():
    """创建不同格式的测试图像"""
    print("创建测试图像...")
    
    # 创建测试目录
    test_dir = "test_images"
    os.makedirs(test_dir, exist_ok=True)
    
    # 1. 灰度图像
    gray_img = np.random.randint(0, 256, (100, 100), dtype=np.uint8)
    Image.fromarray(gray_img, mode='L').save(f"{test_dir}/gray_image.png")
    print("✓ 创建灰度图像")
    
    # 2. RGB图像
    rgb_img = np.random.randint(0, 256, (100, 100, 3), dtype=np.uint8)
    Image.fromarray(rgb_img, mode='RGB').save(f"{test_dir}/rgb_image.png")
    print("✓ 创建RGB图像")
    
    # 3. RGBA图像
    rgba_img = np.random.randint(0, 256, (100, 100, 4), dtype=np.uint8)
    Image.fromarray(rgba_img, mode='RGBA').save(f"{test_dir}/rgba_image.png")
    print("✓ 创建RGBA图像")
    
    return test_dir


def run_tests():
    """运行所有测试"""
    print("开始图像预处理测试...")
    
    # 创建测试图像
    test_dir = create_test_images()
    
    # 测试文件列表
    test_files = [
        f"{test_dir}/gray_image.png",
        f"{test_dir}/rgb_image.png", 
        f"{test_dir}/rgba_image.png"
    ]
    
    success_count = 0
    total_count = len(test_files)
    
    # 测试每个文件
    for test_file in test_files:
        if test_image_preprocessing(test_file):
            success_count += 1
        print()
    
    # 总结
    print(f"=== 测试结果 ===")
    print(f"成功: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 所有测试通过！图像预处理功能正常")
    else:
        print("❌ 部分测试失败，请检查代码")
    
    # 清理测试文件
    import shutil
    shutil.rmtree(test_dir)
    print("已清理测试文件")


def debug_specific_image(image_path):
    """调试特定图像"""
    print(f"=== 调试图像: {image_path} ===")
    
    if not os.path.exists(image_path):
        print(f"文件不存在: {image_path}")
        return
    
    try:
        # 使用不同方法加载图像
        print("1. 使用skimage加载:")
        img_skimage = io.imread(image_path)
        print(f"   形状: {img_skimage.shape}")
        print(f"   数据类型: {img_skimage.dtype}")
        print(f"   值范围: [{img_skimage.min()}, {img_skimage.max()}]")
        
        print("\n2. 使用PIL加载:")
        img_pil = Image.open(image_path)
        print(f"   尺寸: {img_pil.size}")
        print(f"   模式: {img_pil.mode}")
        
        # 转换测试
        print("\n3. 转换测试:")
        test_image_preprocessing(image_path)
        
    except Exception as e:
        print(f"调试失败: {e}")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        # 调试特定图像
        debug_specific_image(sys.argv[1])
    else:
        # 运行标准测试
        run_tests()
