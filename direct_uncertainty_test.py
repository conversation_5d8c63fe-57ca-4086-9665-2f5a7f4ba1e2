#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接运行的不确定性测试脚本
无需任何用户输入，直接使用预设参数
"""

import os
import numpy as np
import torch
import matplotlib.pyplot as plt
from skimage import io, transform
from functools import partial

# ==================== 直接配置参数 ====================
IMAGE_PATH = "/media/ubun/Teacher/hyf/MedSAM/data/FLARE22Train/images_png/FLARE22_Tr_0033_0000/024.png"
BOX_COORDS = "[120,230,190,270]"  # [x1,y1,x2,y2]
CHECKPOINT_PATH = "/media/ubun/Teacher/hyf/MedSAM/work_dir/medsam_model_best.pth"
MC_SAMPLES = 10  # 蒙特卡罗采样次数
OUTPUT_DIR = "/media/ubun/Teacher/hyf/MedSAM/data/FLARE22Train/results_heatmap/"
MODEL_TYPE = "vit_b"  # vit_b, vit_l, vit_h
DEVICE = "cuda:0"  # cuda:0, cpu
# =====================================================


def show_mask(mask, ax, random_color=False):
    if random_color:
        color = np.concatenate([np.random.random(3), np.array([0.6])], axis=0)
    else:
        color = np.array([30/255, 144/255, 255/255, 0.6])
    h, w = mask.shape[-2:]
    mask_image = mask.reshape(h, w, 1) * color.reshape(1, 1, -1)
    ax.imshow(mask_image)


def show_box(box, ax):
    x0, y0 = box[0], box[1]
    w, h = box[2] - box[0], box[3] - box[1]
    ax.add_patch(plt.Rectangle((x0, y0), w, h, edgecolor='green', facecolor=(0,0,0,0), lw=2))


@torch.no_grad()
def medsam_inference(medsam_model, img_embed, box_1024, H, W):
    box_torch = torch.as_tensor(box_1024, dtype=torch.float, device=img_embed.device)
    if len(box_torch.shape) == 2:
        box_torch = box_torch[:, None, :]

    sparse_embeddings, dense_embeddings = medsam_model.prompt_encoder(
        points=None, boxes=box_torch, masks=None,
    )
    low_res_logits, _ = medsam_model.mask_decoder(
        image_embeddings=img_embed,
        image_pe=medsam_model.prompt_encoder.get_dense_pe(),
        sparse_prompt_embeddings=sparse_embeddings,
        dense_prompt_embeddings=dense_embeddings,
        multimask_output=False,
    )

    low_res_pred = torch.sigmoid(low_res_logits)
    low_res_pred = torch.nn.functional.interpolate(
        low_res_pred, size=(H, W), mode="bilinear", align_corners=False,
    )
    low_res_pred = low_res_pred.squeeze().cpu().numpy()
    medsam_seg = (low_res_pred > 0.5).astype(np.uint8)
    return medsam_seg


def load_medsam_model():
    """使用与原项目相同的方式加载MedSAM模型"""
    # 设置设备
    device = torch.device(DEVICE if torch.cuda.is_available() else "cpu")

    # 使用与MedSAM_Inference_no_jiaoohu.py相同的加载方式
    from segment_anything.modeling.sam import Sam
    from segment_anything.modeling.image_encoder import ImageEncoderViT
    from segment_anything.modeling.mask_decoder import MaskDecoder
    from segment_anything.modeling.prompt_encoder import PromptEncoder
    from segment_anything.modeling.transformer import TwoWayTransformer

    # 模型参数
    if MODEL_TYPE == "vit_b":
        encoder_embed_dim = 768
        encoder_depth = 12
        encoder_num_heads = 12
        encoder_global_attn_indexes = [2, 5, 8, 11]
    elif MODEL_TYPE == "vit_l":
        encoder_embed_dim = 1024
        encoder_depth = 24
        encoder_num_heads = 16
        encoder_global_attn_indexes = [5, 11, 17, 23]
    else:  # vit_h
        encoder_embed_dim = 1280
        encoder_depth = 32
        encoder_num_heads = 16
        encoder_global_attn_indexes = [7, 15, 23, 31]

    prompt_embed_dim = 256
    image_size = 1024
    vit_patch_size = 16
    image_embedding_size = image_size // vit_patch_size

    # 创建模型
    sam = Sam(
        image_encoder=ImageEncoderViT(
            depth=encoder_depth,
            embed_dim=encoder_embed_dim,
            img_size=image_size,
            mlp_ratio=4,
            norm_layer=partial(torch.nn.LayerNorm, eps=1e-6),
            num_heads=encoder_num_heads,
            patch_size=vit_patch_size,
            qkv_bias=True,
            use_rel_pos=True,
            global_attn_indexes=encoder_global_attn_indexes,
            window_size=14,
            out_chans=prompt_embed_dim,
            dropout_rate=0.1,
            mc_samples=MC_SAMPLES,
        ),
        prompt_encoder=PromptEncoder(
            embed_dim=prompt_embed_dim,
            image_embedding_size=(image_embedding_size, image_embedding_size),
            input_image_size=(image_size, image_size),
            mask_in_chans=16,
        ),
        mask_decoder=MaskDecoder(
            num_multimask_outputs=3,
            transformer=TwoWayTransformer(
                depth=2, embedding_dim=prompt_embed_dim, mlp_dim=2048, num_heads=8,
            ),
            transformer_dim=prompt_embed_dim,
            iou_head_depth=3,
            iou_head_hidden_dim=256,
        ),
        pixel_mean=[123.675, 116.28, 103.53],
        pixel_std=[58.395, 57.12, 57.375],
    )

    # 添加多尺度特征融合
    if hasattr(sam.image_encoder, 'add_multi_scale_fusion'):
        sam.image_encoder.add_multi_scale_fusion()
        print("✓ 添加了多尺度特征融合模块")

    # 移动到设备
    sam = sam.to(device)

    # 加载权重
    if os.path.exists(CHECKPOINT_PATH):
        print(f"加载模型权重: {CHECKPOINT_PATH}")
        try:
            checkpoint = torch.load(CHECKPOINT_PATH, map_location=device)
            if "model" in checkpoint:
                state_dict = checkpoint["model"]
            else:
                state_dict = checkpoint

            # 使用strict=False来忽略不匹配的键
            missing_keys, unexpected_keys = sam.load_state_dict(state_dict, strict=False)

            if unexpected_keys:
                print(f"⚠️ 忽略了 {len(unexpected_keys)} 个不匹配的键（主要是multi_scale_fusion相关）")

            if missing_keys:
                print(f"⚠️ 缺少 {len(missing_keys)} 个键（将使用随机初始化）")

            print("✓ 模型权重加载成功（已忽略不兼容的键）")

        except Exception as e:
            print(f"❌ 权重加载失败: {str(e)}")
            print("使用随机初始化的权重继续...")
    else:
        print("⚠️ 警告: 模型权重文件不存在，使用随机权重")

    return sam, device


def preprocess_image():
    """预处理图像"""
    print(f"加载图像: {IMAGE_PATH}")
    
    img_np = io.imread(IMAGE_PATH)
    print(f"原始图像形状: {img_np.shape}")
    
    # 转换为3通道
    if len(img_np.shape) == 2:
        img_3c = np.repeat(img_np[:, :, None], 3, axis=-1)
    elif len(img_np.shape) == 3:
        if img_np.shape[2] == 4:
            img_3c = img_np[:, :, :3]
        else:
            img_3c = img_np
    
    H, W, _ = img_3c.shape
    
    # 调整尺寸
    img_1024 = transform.resize(
        img_3c, (1024, 1024), order=3, preserve_range=True, anti_aliasing=True
    ).astype(np.uint8)
    
    # 归一化
    img_1024 = (img_1024 - img_1024.min()) / np.clip(
        img_1024.max() - img_1024.min(), a_min=1e-8, a_max=None
    )
    
    return img_3c, img_1024, H, W


def run_uncertainty_test():
    """运行不确定性测试"""
    print("=" * 60)
    print("MedSAM 不确定性推理")
    print("=" * 60)
    
    # 显示配置
    print(f"📁 图像路径: {IMAGE_PATH}")
    print(f"📦 边界框: {BOX_COORDS}")
    print(f"🏗️ 模型权重: {CHECKPOINT_PATH}")
    print(f"🔢 采样次数: {MC_SAMPLES}")
    print(f"💾 输出目录: {OUTPUT_DIR}")
    print(f"🔧 设备: {DEVICE}")
    
    # 检查文件
    if not os.path.exists(IMAGE_PATH):
        print(f"❌ 图像文件不存在: {IMAGE_PATH}")
        return
    
    try:
        # 加载模型
        print("\n📦 加载模型...")
        medsam_model, device = load_medsam_model()
        medsam_model.eval()
        
        # 预处理图像
        print("\n🖼️ 预处理图像...")
        img_3c, img_1024, H, W = preprocess_image()
        
        # 转换为tensor
        img_1024_tensor = torch.tensor(img_1024).float().permute(2, 0, 1).unsqueeze(0).to(device)
        
        # 解析边界框
        box_coords = [int(x) for x in BOX_COORDS[1:-1].split(',')]
        box_np = np.array([box_coords])
        box_1024 = box_np / np.array([W, H, W, H]) * 1024
        
        print(f"边界框 (原始): {box_np[0]}")
        print(f"边界框 (1024): {box_1024[0]}")
        
        # 不确定性推理
        print(f"\n🔄 开始蒙特卡罗Dropout推理 (采样{MC_SAMPLES}次)...")
        with torch.no_grad():
            image_embedding, uncertainty = medsam_model.image_encoder(img_1024_tensor, mc_dropout=True)
            
            # 统计信息
            uncertainty_np = uncertainty.detach().cpu().numpy()
            print(f"📊 不确定性统计:")
            print(f"   均值: {uncertainty_np.mean():.6f}")
            print(f"   标准差: {uncertainty_np.std():.6f}")
            print(f"   范围: [{uncertainty_np.min():.6f}, {uncertainty_np.max():.6f}]")
        
        # 分割
        print("\n✂️ 执行分割...")
        medsam_seg = medsam_inference(medsam_model, image_embedding, box_1024, H, W)
        print(f"分割区域像素数: {np.sum(medsam_seg)}")
        
        # 保存和可视化
        print(f"\n💾 保存结果到: {OUTPUT_DIR}")
        save_results(img_3c, medsam_seg, uncertainty, box_np[0])
        
        print("\n🎉 不确定性推理完成!")
        
    except Exception as e:
        print(f"❌ 错误: {str(e)}")
        import traceback
        traceback.print_exc()


def save_results(img_3c, medsam_seg, uncertainty, box):
    """保存结果"""
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # 保存分割结果
    seg_path = os.path.join(OUTPUT_DIR, "segmentation_result.png")
    io.imsave(seg_path, medsam_seg * 255, check_contrast=False)
    
    # 处理不确定性
    uncertainty_np = uncertainty.detach().cpu().numpy()
    mean_uncertainty = uncertainty_np.mean(axis=1)
    mean_uncertainty = (mean_uncertainty - mean_uncertainty.min()) / (mean_uncertainty.max() - mean_uncertainty.min())
    
    # 创建可视化
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 原始图像 + 边界框
    axes[0, 0].imshow(img_3c)
    show_box(box, axes[0, 0])
    axes[0, 0].set_title("Input Image + Bounding Box", fontsize=14)
    axes[0, 0].axis('off')
    
    # 分割结果
    axes[0, 1].imshow(img_3c)
    show_mask(medsam_seg, axes[0, 1])
    show_box(box, axes[0, 1])
    axes[0, 1].set_title("Segmentation Result", fontsize=14)
    axes[0, 1].axis('off')
    
    # 不确定性热力图
    im1 = axes[1, 0].imshow(mean_uncertainty[0], cmap='hot', interpolation='nearest')
    axes[1, 0].set_title("Uncertainty Heatmap", fontsize=14)
    axes[1, 0].axis('off')
    plt.colorbar(im1, ax=axes[1, 0], label='Uncertainty', shrink=0.8)
    
    # 叠加显示
    axes[1, 1].imshow(img_3c)
    show_mask(medsam_seg, axes[1, 1])
    
    uncertainty_resized = transform.resize(
        mean_uncertainty[0], (img_3c.shape[0], img_3c.shape[1]), 
        order=1, preserve_range=True, anti_aliasing=True
    )
    uncertainty_overlay = uncertainty_resized.copy()
    uncertainty_overlay[medsam_seg == 0] = 0
    
    im2 = axes[1, 1].imshow(uncertainty_overlay, cmap='jet', alpha=0.6, interpolation='nearest')
    axes[1, 1].set_title("Segmentation + Uncertainty Overlay", fontsize=14)
    axes[1, 1].axis('off')
    plt.colorbar(im2, ax=axes[1, 1], label='Uncertainty in Segmented Region', shrink=0.8)
    
    plt.tight_layout()
    
    # 保存可视化
    viz_path = os.path.join(OUTPUT_DIR, "uncertainty_visualization.png")
    plt.savefig(viz_path, dpi=300, bbox_inches='tight')
    
    # 保存热力图
    plt.figure(figsize=(8, 6))
    plt.imshow(mean_uncertainty[0], cmap='hot', interpolation='nearest')
    plt.colorbar(label='Uncertainty')
    plt.title('Uncertainty Heatmap')
    plt.axis('off')
    
    heatmap_path = os.path.join(OUTPUT_DIR, "uncertainty_heatmap.png")
    plt.savefig(heatmap_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✓ 分割结果: {seg_path}")
    print(f"✓ 可视化: {viz_path}")
    print(f"✓ 热力图: {heatmap_path}")
    
    # 显示结果
    plt.show()


if __name__ == "__main__":
    run_uncertainty_test()
