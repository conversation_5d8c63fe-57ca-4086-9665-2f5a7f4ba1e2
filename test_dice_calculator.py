#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Dice系数计算器
创建测试数据并验证功能
"""

import os
import numpy as np
from PIL import Image
import tempfile
import shutil
from pathlib import Path

# 导入我们的计算器
from simple_dice_calculator import calculate_dice_scores, compute_dice_coefficient


def create_test_images():
    """创建测试图像数据"""
    # 创建临时文件夹
    temp_dir = tempfile.mkdtemp()
    folder1 = os.path.join(temp_dir, "ground_truth")
    folder2 = os.path.join(temp_dir, "predictions")
    
    os.makedirs(folder1, exist_ok=True)
    os.makedirs(folder2, exist_ok=True)
    
    # 创建测试图像
    test_cases = [
        {
            'name': 'perfect_match',
            'gt': np.array([[1, 1, 0], [1, 1, 0], [0, 0, 0]], dtype=np.uint8) * 255,
            'pred': np.array([[1, 1, 0], [1, 1, 0], [0, 0, 0]], dtype=np.uint8) * 255,
            'expected_dice': 1.0
        },
        {
            'name': 'no_overlap',
            'gt': np.array([[1, 1, 0], [1, 1, 0], [0, 0, 0]], dtype=np.uint8) * 255,
            'pred': np.array([[0, 0, 1], [0, 0, 1], [1, 1, 1]], dtype=np.uint8) * 255,
            'expected_dice': 0.0
        },
        {
            'name': 'partial_overlap',
            'gt': np.array([[1, 1, 0], [1, 1, 0], [0, 0, 0]], dtype=np.uint8) * 255,
            'pred': np.array([[1, 0, 0], [1, 0, 0], [0, 0, 0]], dtype=np.uint8) * 255,
            'expected_dice': 0.5  # 2*2/(4+2) = 4/6 = 0.667
        },
        {
            'name': 'empty_masks',
            'gt': np.zeros((3, 3), dtype=np.uint8),
            'pred': np.zeros((3, 3), dtype=np.uint8),
            'expected_dice': np.nan
        }
    ]
    
    for case in test_cases:
        # 保存真实标签
        gt_image = Image.fromarray(case['gt'], mode='L')
        gt_path = os.path.join(folder1, f"{case['name']}.png")
        gt_image.save(gt_path)
        
        # 保存预测结果
        pred_image = Image.fromarray(case['pred'], mode='L')
        pred_path = os.path.join(folder2, f"{case['name']}.png")
        pred_image.save(pred_path)
    
    return folder1, folder2, temp_dir, test_cases


def test_dice_calculation():
    """测试Dice系数计算功能"""
    print("=== 测试Dice系数计算功能 ===")
    
    # 创建测试数据
    folder1, folder2, temp_dir, test_cases = create_test_images()
    
    try:
        # 测试单个Dice计算
        print("\n1. 测试单个Dice计算:")
        for case in test_cases:
            gt_mask = (case['gt'] > 0).astype(bool)
            pred_mask = (case['pred'] > 0).astype(bool)
            
            calculated_dice = compute_dice_coefficient(gt_mask, pred_mask)
            expected_dice = case['expected_dice']
            
            print(f"  {case['name']}:")
            print(f"    计算值: {calculated_dice:.4f}")
            print(f"    期望值: {expected_dice:.4f}")
            
            if np.isnan(expected_dice):
                assert np.isnan(calculated_dice), f"期望NaN但得到{calculated_dice}"
            else:
                assert abs(calculated_dice - expected_dice) < 0.01, \
                    f"Dice计算错误: 期望{expected_dice}, 得到{calculated_dice}"
            
            print(f"    ✓ 通过")
        
        # 测试批量计算
        print(f"\n2. 测试批量计算:")
        print(f"  文件夹1: {folder1}")
        print(f"  文件夹2: {folder2}")
        
        output_file = os.path.join(temp_dir, "test_results.csv")
        results = calculate_dice_scores(folder1, folder2, output_file)
        
        # 验证结果
        assert len(results) == len(test_cases), f"结果数量不匹配: 期望{len(test_cases)}, 得到{len(results)}"
        
        print(f"  ✓ 成功处理{len(results)}个文件对")
        print(f"  ✓ 结果已保存到{output_file}")
        
        # 检查CSV文件是否存在
        assert os.path.exists(output_file), "CSV文件未创建"
        print(f"  ✓ CSV文件创建成功")
        
        print(f"\n=== 所有测试通过! ===")
        
    finally:
        # 清理临时文件
        shutil.rmtree(temp_dir)
        print(f"已清理临时文件: {temp_dir}")


def test_file_matching():
    """测试文件匹配功能"""
    print("\n=== 测试文件匹配功能 ===")
    
    temp_dir = tempfile.mkdtemp()
    folder1 = os.path.join(temp_dir, "folder1")
    folder2 = os.path.join(temp_dir, "folder2")
    
    os.makedirs(folder1, exist_ok=True)
    os.makedirs(folder2, exist_ok=True)
    
    try:
        # 创建测试文件
        test_image = np.ones((10, 10), dtype=np.uint8) * 255
        
        # 文件夹1中的文件
        files1 = ["image1.png", "image2.jpg", "scan1.nii.gz"]
        for filename in files1:
            if filename.endswith('.nii.gz'):
                # 简单创建一个文本文件模拟nii.gz（实际测试中可以跳过）
                with open(os.path.join(folder1, filename), 'w') as f:
                    f.write("dummy nii file")
            else:
                Image.fromarray(test_image, mode='L').save(os.path.join(folder1, filename))
        
        # 文件夹2中的文件（部分匹配）
        files2 = ["image1.png", "image3.jpg", "scan1.nii.gz"]  # image2不匹配，image3是新的
        for filename in files2:
            if filename.endswith('.nii.gz'):
                with open(os.path.join(folder2, filename), 'w') as f:
                    f.write("dummy nii file")
            else:
                Image.fromarray(test_image, mode='L').save(os.path.join(folder2, filename))
        
        # 测试文件匹配
        from simple_dice_calculator import find_matching_files
        matching_files = find_matching_files(folder1, folder2)
        
        print(f"文件夹1中的文件: {files1}")
        print(f"文件夹2中的文件: {files2}")
        print(f"匹配的文件对: {[name for _, _, name in matching_files]}")
        
        # 应该匹配image1和scan1
        expected_matches = {"image1", "scan1"}
        actual_matches = {name for _, _, name in matching_files}
        
        assert actual_matches == expected_matches, \
            f"文件匹配错误: 期望{expected_matches}, 得到{actual_matches}"
        
        print(f"✓ 文件匹配测试通过")
        
    finally:
        shutil.rmtree(temp_dir)


def run_all_tests():
    """运行所有测试"""
    print("开始运行Dice计算器测试...")
    
    try:
        test_file_matching()
        test_dice_calculation()
        print(f"\n🎉 所有测试都通过了!")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        raise


if __name__ == "__main__":
    run_all_tests()
