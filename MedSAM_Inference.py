#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MedSAM 简单推理
去掉不确定性估计，确保能够正常运行
"""

import numpy as np
import matplotlib.pyplot as plt
import os
import torch
from skimage import io, transform
import torch.nn.functional as F
from functools import partial

# ==================== 直接配置参数 ====================
IMAGE_PATH = "/media/ubun/Teacher/hyf/MedSAM/data/FLARE22Train/images_png/FLARE22_Tr_0033_0000/024.png"
CHECKPOINT_PATH = "/media/ubun/Teacher/hyf/MedSAM/work_dir/medsam_model_best.pth"

BOUNDING_BOX = [120, 230, 190, 270]  # [x1, y1, x2, y2]
DEVICE = "cuda:0"
MODEL_TYPE = "vit_b"
# ================================================


def show_mask(mask, ax, random_color=False):
    """显示分割掩码"""
    if random_color:
        color = np.concatenate([np.random.random(3), np.array([0.6])], axis=0)
    else:
        color = np.array([251 / 255, 252 / 255, 30 / 255, 0.6])
    h, w = mask.shape[-2:]
    mask_image = mask.reshape(h, w, 1) * color.reshape(1, 1, -1)
    ax.imshow(mask_image)


def show_box(box, ax):
    """显示边界框"""
    x0, y0 = box[0], box[1]
    w, h = box[2] - box[0], box[3] - box[1]
    ax.add_patch(
        plt.Rectangle((x0, y0), w, h, edgecolor="blue", facecolor=(0, 0, 0, 0), lw=2)
    )


@torch.no_grad()
def medsam_inference(medsam_model, img_embed, box_1024, H, W):
    """MedSAM推理函数"""
    box_torch = torch.as_tensor(box_1024, dtype=torch.float, device=img_embed.device)
    if len(box_torch.shape) == 2:
        box_torch = box_torch[:, None, :]

    sparse_embeddings, dense_embeddings = medsam_model.prompt_encoder(
        points=None, boxes=box_torch, masks=None,
    )
    low_res_logits, _ = medsam_model.mask_decoder(
        image_embeddings=img_embed,
        image_pe=medsam_model.prompt_encoder.get_dense_pe(),
        sparse_prompt_embeddings=sparse_embeddings,
        dense_prompt_embeddings=dense_embeddings,
        multimask_output=False,
    )

    low_res_pred = torch.sigmoid(low_res_logits)
    low_res_pred = F.interpolate(
        low_res_pred, size=(H, W), mode="bilinear", align_corners=False,
    )
    low_res_pred = low_res_pred.squeeze().cpu().numpy()
    medsam_seg = (low_res_pred > 0.5).astype(np.uint8)
    return medsam_seg


def load_medsam_model():
    """加载MedSAM模型"""
    print(f"🔧 设备: {DEVICE}")
    print(f"📦 模型类型: {MODEL_TYPE}")
    
    device = torch.device(DEVICE if torch.cuda.is_available() else "cpu")
    
    # 导入模块
    from segment_anything.modeling.sam import Sam
    from segment_anything.modeling.image_encoder import ImageEncoderViT
    from segment_anything.modeling.mask_decoder import MaskDecoder
    from segment_anything.modeling.prompt_encoder import PromptEncoder
    from segment_anything.modeling.transformer import TwoWayTransformer

    # 模型参数
    if MODEL_TYPE == "vit_b":
        encoder_embed_dim = 768
        encoder_depth = 12
        encoder_num_heads = 12
        encoder_global_attn_indexes = [2, 5, 8, 11]
    elif MODEL_TYPE == "vit_l":
        encoder_embed_dim = 1024
        encoder_depth = 24
        encoder_num_heads = 16
        encoder_global_attn_indexes = [5, 11, 17, 23]
    else:  # vit_h
        encoder_embed_dim = 1280
        encoder_depth = 32
        encoder_num_heads = 16
        encoder_global_attn_indexes = [7, 15, 23, 31]

    prompt_embed_dim = 256
    image_size = 1024
    vit_patch_size = 16
    image_embedding_size = image_size // vit_patch_size

    # 创建模型
    sam = Sam(
        image_encoder=ImageEncoderViT(
            depth=encoder_depth,
            embed_dim=encoder_embed_dim,
            img_size=image_size,
            mlp_ratio=4,
            norm_layer=partial(torch.nn.LayerNorm, eps=1e-6),
            num_heads=encoder_num_heads,
            patch_size=vit_patch_size,
            qkv_bias=True,
            use_rel_pos=True,
            global_attn_indexes=encoder_global_attn_indexes,
            window_size=14,
            out_chans=prompt_embed_dim,
        ),
        prompt_encoder=PromptEncoder(
            embed_dim=prompt_embed_dim,
            image_embedding_size=(image_embedding_size, image_embedding_size),
            input_image_size=(image_size, image_size),
            mask_in_chans=16,
        ),
        mask_decoder=MaskDecoder(
            num_multimask_outputs=3,
            transformer=TwoWayTransformer(
                depth=2, embedding_dim=prompt_embed_dim, mlp_dim=2048, num_heads=8,
            ),
            transformer_dim=prompt_embed_dim,
            iou_head_depth=3,
            iou_head_hidden_dim=256,
        ),
        pixel_mean=[123.675, 116.28, 103.53],
        pixel_std=[58.395, 57.12, 57.375],
    )

    # 移动到设备
    sam = sam.to(device)

    # 加载权重
    if os.path.exists(CHECKPOINT_PATH):
        print(f"📥 加载模型权重: {CHECKPOINT_PATH}")
        try:
            checkpoint = torch.load(CHECKPOINT_PATH, map_location=device)
            if "model" in checkpoint:
                state_dict = checkpoint["model"]
            else:
                state_dict = checkpoint
            
            missing_keys, unexpected_keys = sam.load_state_dict(state_dict, strict=False)
            
            if unexpected_keys:
                print(f"⚠️ 忽略了 {len(unexpected_keys)} 个不匹配的键")
            
            print("✅ 模型权重加载成功")
            
        except Exception as e:
            print(f"❌ 权重加载失败: {str(e)}")
            print("使用随机初始化的权重继续...")
    else:
        print("⚠️ 模型权重文件不存在，使用随机权重")

    return sam, device


def preprocess_image():
    """预处理图像"""
    print(f"🖼️ 加载图像: {IMAGE_PATH}")
    
    if not os.path.exists(IMAGE_PATH):
        raise FileNotFoundError(f"图像文件不存在: {IMAGE_PATH}")
    
    img_np = io.imread(IMAGE_PATH)
    print(f"   原始图像形状: {img_np.shape}")
    
    # 转换为3通道
    if len(img_np.shape) == 2:
        img_3c = np.repeat(img_np[:, :, None], 3, axis=-1)
        print("   灰度图像已转换为3通道")
    elif len(img_np.shape) == 3:
        if img_np.shape[2] == 4:
            img_3c = img_np[:, :, :3]
            print("   RGBA图像已转换为RGB")
        else:
            img_3c = img_np
    
    H, W, _ = img_3c.shape
    print(f"   预处理后图像尺寸: {H}x{W}x{img_3c.shape[2]}")
    
    # 调整尺寸到1024x1024
    img_1024 = transform.resize(
        img_3c, (1024, 1024), order=3, preserve_range=True, anti_aliasing=True
    ).astype(np.uint8)
    
    # 归一化
    img_1024 = (img_1024 - img_1024.min()) / np.clip(
        img_1024.max() - img_1024.min(), a_min=1e-8, a_max=None
    )
    
    return img_3c, img_1024, H, W


def display_results(img_3c, medsam_seg, box):
    """直接显示结果，不保存文件"""
    print(f"📊 显示分割结果...")

    # 计算分割统计信息
    total_pixels = img_3c.shape[0] * img_3c.shape[1]
    seg_pixels = np.sum(medsam_seg)
    seg_ratio = seg_pixels / total_pixels * 100

    # 创建可视化
    fig, axes = plt.subplots(1, 3, figsize=(20, 7))

    # 1. 原始图像 + 边界框
    axes[0].imshow(img_3c)
    show_box(box, axes[0])
    axes[0].set_title("Input Image + Bounding Box", fontsize=16, fontweight='bold')
    axes[0].axis('off')

    # 2. 分割结果
    axes[1].imshow(img_3c)
    show_mask(medsam_seg, axes[1])
    show_box(box, axes[1])
    axes[1].set_title("Segmentation Result", fontsize=16, fontweight='bold')
    axes[1].axis('off')

    # 3. 只显示分割掩码
    axes[2].imshow(medsam_seg, cmap='gray')
    axes[2].set_title("Segmentation Mask", fontsize=16, fontweight='bold')
    axes[2].axis('off')

    # 添加整体标题和统计信息
    fig.suptitle(f'MedSAM Segmentation Results\n'
                f'Segmented Pixels: {seg_pixels:,} ({seg_ratio:.2f}% of image)',
                fontsize=18, fontweight='bold', y=0.95)

    plt.tight_layout()

    # 直接显示结果
    plt.show()

    print(f"✅ 结果已显示")
    print(f"📈 分割统计:")
    print(f"   总像素数: {total_pixels:,}")
    print(f"   分割像素数: {seg_pixels:,}")
    print(f"   分割占比: {seg_ratio:.2f}%")


def main():
    """主函数 - 简单推理，无不确定性估计"""
    print("=" * 60)
    print("🔥 MedSAM 简单推理")
    print("=" * 60)
    
    print(f"📁 图像路径: {IMAGE_PATH}")
    print(f"📦 边界框: {BOUNDING_BOX}")
    print(f"📊 显示模式: 直接使用matplotlib显示")
    
    try:
        # 1. 加载模型
        print("\n📦 加载模型...")
        medsam_model, device = load_medsam_model()
        medsam_model.eval()  # 设置为评估模式
        
        # 2. 预处理图像
        print("\n🖼️ 预处理图像...")
        img_3c, img_1024, H, W = preprocess_image()
        
        # 转换为tensor
        img_1024_tensor = torch.tensor(img_1024).float().permute(2, 0, 1).unsqueeze(0).to(device)
        print(f"   输入tensor形状: {img_1024_tensor.shape}")
        
        # 3. 处理边界框
        box_np = np.array([BOUNDING_BOX])
        box_1024 = box_np / np.array([W, H, W, H]) * 1024
        print(f"📦 边界框 (原始): {box_np[0]}")
        print(f"   边界框 (1024): {box_1024[0]}")
        
        # 4. 图像编码
        print(f"\n🔄 开始图像编码...")
        with torch.no_grad():
            image_embedding = medsam_model.image_encoder(img_1024_tensor)
            print(f"   图像嵌入形状: {image_embedding.shape}")
        
        # 5. 执行分割
        print("\n✂️ 执行分割...")
        medsam_seg = medsam_inference(medsam_model, image_embedding, box_1024, H, W)
        print(f"   分割区域像素数: {np.sum(medsam_seg)}")
        print(f"   分割区域占比: {np.sum(medsam_seg) / (H * W) * 100:.2f}%")
        
        # 6. 显示结果
        print(f"\n📊 显示结果...")
        display_results(img_3c, medsam_seg, box_np[0])
        
        print("\n🎉 推理完成!")
        
    except Exception as e:
        print(f"\n❌ 错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
