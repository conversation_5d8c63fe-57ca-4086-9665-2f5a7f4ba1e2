# 分割评估工具集

这个工具集用于计算两个文件夹中同名图片的多种分割评估指标，包括Dice、IoU、Sensitivity、PPV等，常用于医学图像分割结果的全面评估。

## 功能特点

- **多种评估指标**：Dice、IoU、Sensitivity、PPV、Specificity、Accuracy等
- **支持多种图像格式**：PNG, JPG, JPEG, BMP, TIFF, NII, NII.GZ
- **自动匹配同名文件**（忽略扩展名差异）
- **详细统计分析**：均值、标准差、中位数、四分位数等
- **可视化图表**：箱线图、相关性热力图
- **输出结果到CSV文件**
- **支持命令行和交互式使用**

## 文件说明

1. **enhanced_segmentation_evaluator.py** - 增强版评估器，支持全面的指标计算和可视化
2. **simple_dice_calculator.py** - 更新版简化计算器，支持多种指标
3. **calculate_dice_between_folders.py** - 完整版本，支持命令行参数
4. **utils/SurfaceDice.py** - 包含Dice系数计算函数

## 支持的评估指标

### 主要指标
- **Dice系数**：2×TP/(2×TP+FP+FN)，衡量重叠程度
- **IoU (Jaccard)**：TP/(TP+FP+FN)，交并比
- **Sensitivity (Recall)**：TP/(TP+FN)，敏感性/召回率
- **PPV (Precision)**：TP/(TP+FP)，阳性预测值/精确率

### 辅助指标
- **Specificity**：TN/(TN+FP)，特异性
- **NPV**：TN/(TN+FN)，阴性预测值
- **Accuracy**：(TP+TN)/(TP+FP+FN+TN)，准确率
- **F1-Score**：与Dice系数相同
- **Balanced Accuracy**：(Sensitivity+Specificity)/2
- **MCC**：Matthews相关系数
- **Volume Similarity**：体积相似性

## 使用方法

### 方法1: 使用增强版评估器（推荐）

```bash
python enhanced_segmentation_evaluator.py
```

交互式输入：
- 真实标签文件夹路径
- 预测结果文件夹路径
- 输出文件名（可选）
- 是否创建可视化图表

**特点**：
- 计算所有评估指标
- 生成统计摘要
- 创建可视化图表
- 详细的进度显示

### 方法2: 使用简化版本

```bash
python simple_dice_calculator.py
```

交互式输入：
- 第一个文件夹路径（真实标签）
- 第二个文件夹路径（预测结果）
- 输出文件名（可选）

**特点**：
- 计算主要指标（Dice、IoU、Sen、PPV等）
- 快速处理
- 简洁输出

### 方法3: 使用完整版本（命令行）

```bash
python calculate_dice_between_folders.py folder1 folder2 --output results.csv
```

参数说明：
- `folder1`: 第一个文件夹路径（真实标签）
- `folder2`: 第二个文件夹路径（预测结果）
- `--output`: 输出CSV文件路径（可选）
- `--threshold`: 二值化阈值（可选，默认0.5）

## 输入要求

1. **文件夹结构**：两个文件夹中应包含同名的图像文件
2. **图像格式**：支持常见的图像格式和医学图像格式
3. **图像内容**：图像应为二值掩码或可转换为二值掩码的灰度图像

## 输出结果

### 详细结果CSV文件
包含每个文件对的所有指标：
- **基本信息**：文件名、路径、图像尺寸、前景像素数
- **主要指标**：Dice、IoU、Sensitivity、PPV、Specificity、Accuracy
- **辅助指标**：NPV、F1-Score、Balanced Accuracy、MCC、Volume Similarity
- **混淆矩阵**：TP、FP、FN、TN

### 统计摘要CSV文件
包含所有指标的统计信息：
- 均值和标准差
- 最小值、最大值、中位数
- 25%和75%分位数

### 可视化图表（可选）
- **指标分布箱线图**：显示各指标的分布情况
- **指标相关性热力图**：显示指标间的相关性

### 控制台输出
实时显示：
- 处理进度
- 每个文件的主要指标
- 最终统计摘要

## 使用示例

### 示例1: 比较分割结果

```python
# 在simple_dice_calculator.py中直接修改路径
folder1 = "data/ground_truth"  # 真实标签
folder2 = "data/predictions"   # 预测结果
results = calculate_dice_scores(folder1, folder2, "segmentation_results.csv")
```

### 示例2: 批量处理

```bash
# 使用命令行版本
python calculate_dice_between_folders.py \
    /path/to/ground_truth \
    /path/to/predictions \
    --output evaluation_results.csv
```

## 注意事项

1. **文件命名**：确保两个文件夹中的对应文件有相同的基础名称（不包括扩展名）
2. **图像尺寸**：对应的图像必须有相同的尺寸
3. **数据类型**：图像会自动转换为布尔掩码（非零值视为前景）
4. **NII.GZ文件**：程序会自动处理.nii.gz文件的特殊命名

## 错误处理

程序包含完善的错误处理机制：
- 文件夹不存在
- 图像加载失败
- 尺寸不匹配
- 格式不支持

遇到错误时会在控制台显示详细信息并跳过有问题的文件。

## 依赖库

确保安装以下Python库：

```bash
pip install numpy pandas pillow nibabel
```

## Dice系数说明

Dice系数（Dice Coefficient）是评估两个二值图像相似度的常用指标：

```
Dice = 2 * |A ∩ B| / (|A| + |B|)
```

其中：
- A 和 B 是两个二值掩码
- |A ∩ B| 是交集的像素数
- |A| 和 |B| 是各自的前景像素数

Dice系数范围为0到1：
- 0表示完全不重叠
- 1表示完全重叠

## 常见问题

**Q: 为什么某些文件被跳过？**
A: 可能的原因包括：文件格式不支持、图像尺寸不匹配、文件损坏等。

**Q: 如何处理不同格式的图像？**
A: 程序会自动检测文件格式并使用相应的加载方法。

**Q: 可以处理3D图像吗？**
A: 是的，程序支持NII格式的3D医学图像。

**Q: 如何修改二值化阈值？**
A: 在完整版本中使用--threshold参数，或在代码中修改相关函数。
