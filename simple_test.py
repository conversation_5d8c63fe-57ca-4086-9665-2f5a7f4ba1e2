#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
验证分割评估功能是否正常
"""

import numpy as np

def compute_comprehensive_metrics(mask_gt, mask_pred):
    """
    计算全面的分割评估指标
    """
    # 确保输入是布尔类型
    mask_gt = mask_gt.astype(bool)
    mask_pred = mask_pred.astype(bool)
    
    # 计算混淆矩阵
    tp = np.sum(mask_gt & mask_pred)  # True Positive
    fp = np.sum(~mask_gt & mask_pred)  # False Positive
    fn = np.sum(mask_gt & ~mask_pred)  # False Negative
    tn = np.sum(~mask_gt & ~mask_pred)  # True Negative
    
    metrics = {}
    
    # 基础混淆矩阵指标
    metrics['TP'] = int(tp)
    metrics['FP'] = int(fp)
    metrics['FN'] = int(fn)
    metrics['TN'] = int(tn)
    
    # Dice系数
    if tp + fp + fn == 0:
        metrics['Dice'] = 1.0 if tp == 0 else np.NaN
    else:
        metrics['Dice'] = 2 * tp / (2 * tp + fp + fn)
    
    # IoU
    if tp + fp + fn == 0:
        metrics['IoU'] = 1.0 if tp == 0 else np.NaN
    else:
        metrics['IoU'] = tp / (tp + fp + fn)
    
    # Sensitivity
    if tp + fn == 0:
        metrics['Sensitivity'] = np.NaN
    else:
        metrics['Sensitivity'] = tp / (tp + fn)
    
    # PPV
    if tp + fp == 0:
        metrics['PPV'] = np.NaN
    else:
        metrics['PPV'] = tp / (tp + fp)
    
    # Specificity
    if tn + fp == 0:
        metrics['Specificity'] = np.NaN
    else:
        metrics['Specificity'] = tn / (tn + fp)
    
    # Accuracy
    total = tp + fp + fn + tn
    if total == 0:
        metrics['Accuracy'] = np.NaN
    else:
        metrics['Accuracy'] = (tp + tn) / total
    
    return metrics


def test_metrics():
    """测试指标计算"""
    print("=== 测试分割评估指标 ===")
    
    # 测试用例1: 完全匹配
    print("\n1. 完全匹配测试:")
    gt1 = np.array([[1, 1, 0], [1, 1, 0], [0, 0, 0]], dtype=bool)
    pred1 = np.array([[1, 1, 0], [1, 1, 0], [0, 0, 0]], dtype=bool)
    metrics1 = compute_comprehensive_metrics(gt1, pred1)
    
    print(f"   Dice: {metrics1['Dice']:.4f}")
    print(f"   IoU: {metrics1['IoU']:.4f}")
    print(f"   Sensitivity: {metrics1['Sensitivity']:.4f}")
    print(f"   PPV: {metrics1['PPV']:.4f}")
    print(f"   Specificity: {metrics1['Specificity']:.4f}")
    print(f"   Accuracy: {metrics1['Accuracy']:.4f}")
    
    # 测试用例2: 部分重叠
    print("\n2. 部分重叠测试:")
    gt2 = np.array([[1, 1, 0], [1, 1, 0], [0, 0, 0]], dtype=bool)
    pred2 = np.array([[1, 0, 0], [1, 0, 0], [0, 0, 0]], dtype=bool)
    metrics2 = compute_comprehensive_metrics(gt2, pred2)
    
    print(f"   TP: {metrics2['TP']}, FP: {metrics2['FP']}, FN: {metrics2['FN']}, TN: {metrics2['TN']}")
    print(f"   Dice: {metrics2['Dice']:.4f}")
    print(f"   IoU: {metrics2['IoU']:.4f}")
    print(f"   Sensitivity: {metrics2['Sensitivity']:.4f}")
    print(f"   PPV: {metrics2['PPV']:.4f}")
    print(f"   Specificity: {metrics2['Specificity']:.4f}")
    print(f"   Accuracy: {metrics2['Accuracy']:.4f}")
    
    # 测试用例3: 完全不匹配
    print("\n3. 完全不匹配测试:")
    gt3 = np.array([[1, 1, 0], [1, 1, 0], [0, 0, 0]], dtype=bool)
    pred3 = np.array([[0, 0, 1], [0, 0, 1], [1, 1, 1]], dtype=bool)
    metrics3 = compute_comprehensive_metrics(gt3, pred3)
    
    print(f"   TP: {metrics3['TP']}, FP: {metrics3['FP']}, FN: {metrics3['FN']}, TN: {metrics3['TN']}")
    print(f"   Dice: {metrics3['Dice']:.4f}")
    print(f"   IoU: {metrics3['IoU']:.4f}")
    print(f"   Sensitivity: {metrics3['Sensitivity']:.4f}")
    print(f"   PPV: {metrics3['PPV']:.4f}")
    print(f"   Specificity: {metrics3['Specificity']:.4f}")
    print(f"   Accuracy: {metrics3['Accuracy']:.4f}")
    
    print("\n✓ 所有测试完成！")
    print("\n代码功能正常，支持的指标包括：")
    print("- Dice系数：衡量重叠程度")
    print("- IoU：交并比")
    print("- Sensitivity：敏感性/召回率")
    print("- PPV：阳性预测值/精确率")
    print("- Specificity：特异性")
    print("- Accuracy：准确率")


if __name__ == "__main__":
    test_metrics()
