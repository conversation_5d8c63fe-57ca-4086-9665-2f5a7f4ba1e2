#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像预处理工具
用于MedSAM推理时的图像预处理，确保输入图像格式正确
"""

import numpy as np
import torch
from skimage import io, transform
from PIL import Image


def load_and_preprocess_image(image_path, target_size=1024, device='cpu'):
    """
    加载并预处理图像，确保输出为3通道RGB格式
    
    参数:
        image_path: 图像文件路径
        target_size: 目标尺寸 (默认1024)
        device: PyTorch设备
    
    返回:
        img_tensor: 预处理后的tensor [1, 3, target_size, target_size]
        original_shape: 原始图像尺寸 (H, W)
        img_3c: 3通道图像数组 (H, W, 3)
    """
    
    # 加载图像
    img_np = io.imread(image_path)
    print(f"原始图像形状: {img_np.shape}")
    
    # 转换为3通道RGB格式
    img_3c = convert_to_3channel(img_np)
    
    H, W, _ = img_3c.shape
    print(f"转换后图像尺寸: {H}x{W}, 通道数: {img_3c.shape[2]}")
    
    # 调整尺寸到目标大小
    img_resized = transform.resize(
        img_3c, (target_size, target_size), 
        order=3, preserve_range=True, anti_aliasing=True
    ).astype(np.uint8)
    
    # 归一化到[0, 1]
    img_normalized = normalize_image(img_resized)
    
    # 转换为PyTorch tensor
    img_tensor = numpy_to_tensor(img_normalized, device)
    
    print(f"最终tensor形状: {img_tensor.shape}")
    
    return img_tensor, (H, W), img_3c


def convert_to_3channel(img_np):
    """
    将任意通道数的图像转换为3通道RGB格式
    
    参数:
        img_np: 输入图像数组
    
    返回:
        img_3c: 3通道图像数组
    """
    
    if len(img_np.shape) == 2:
        # 灰度图像 (H, W) -> (H, W, 3)
        print("检测到灰度图像，转换为3通道")
        img_3c = np.repeat(img_np[:, :, None], 3, axis=-1)
        
    elif len(img_np.shape) == 3:
        if img_np.shape[2] == 1:
            # 单通道图像 (H, W, 1) -> (H, W, 3)
            print("检测到单通道图像，转换为3通道")
            img_3c = np.repeat(img_np, 3, axis=-1)
            
        elif img_np.shape[2] == 3:
            # RGB图像，直接使用
            print("检测到RGB图像，直接使用")
            img_3c = img_np
            
        elif img_np.shape[2] == 4:
            # RGBA图像，去掉透明度通道
            print("检测到RGBA图像，去掉透明度通道")
            img_3c = img_np[:, :, :3]
            
        else:
            # 其他多通道图像，只取前3个通道
            print(f"检测到{img_np.shape[2]}通道图像，只取前3个通道")
            img_3c = img_np[:, :, :3]
            
    else:
        raise ValueError(f"不支持的图像维度: {img_np.shape}")
    
    # 确保数据类型正确
    if img_3c.dtype != np.uint8:
        # 如果不是uint8，进行转换
        if img_3c.max() <= 1.0:
            # 如果值在[0,1]范围内，转换为[0,255]
            img_3c = (img_3c * 255).astype(np.uint8)
        else:
            # 否则直接转换类型
            img_3c = img_3c.astype(np.uint8)
    
    return img_3c


def normalize_image(img):
    """
    归一化图像到[0, 1]范围
    
    参数:
        img: 输入图像数组
    
    返回:
        normalized_img: 归一化后的图像
    """
    img_min = img.min()
    img_max = img.max()
    
    if img_max == img_min:
        # 避免除零错误
        return np.zeros_like(img, dtype=np.float32)
    
    normalized_img = (img - img_min) / (img_max - img_min)
    return normalized_img.astype(np.float32)


def numpy_to_tensor(img_np, device='cpu'):
    """
    将numpy数组转换为PyTorch tensor
    
    参数:
        img_np: numpy图像数组 (H, W, C)
        device: PyTorch设备
    
    返回:
        tensor: PyTorch tensor (1, C, H, W)
    """
    # 转换维度顺序: (H, W, C) -> (C, H, W)
    img_tensor = torch.tensor(img_np).float().permute(2, 0, 1)
    
    # 添加batch维度: (C, H, W) -> (1, C, H, W)
    img_tensor = img_tensor.unsqueeze(0)
    
    # 移动到指定设备
    img_tensor = img_tensor.to(device)
    
    return img_tensor


def preprocess_image_pil(image_path, target_size=1024):
    """
    使用PIL进行图像预处理（备用方法）
    
    参数:
        image_path: 图像文件路径
        target_size: 目标尺寸
    
    返回:
        img_array: 预处理后的numpy数组
    """
    
    # 使用PIL加载图像
    img_pil = Image.open(image_path)
    
    # 转换为RGB格式
    if img_pil.mode != 'RGB':
        print(f"图像模式: {img_pil.mode} -> RGB")
        img_pil = img_pil.convert('RGB')
    
    # 调整尺寸
    img_pil = img_pil.resize((target_size, target_size), Image.LANCZOS)
    
    # 转换为numpy数组
    img_array = np.array(img_pil)
    
    return img_array


def debug_image_info(img_path):
    """
    调试函数：显示图像的详细信息
    
    参数:
        img_path: 图像文件路径
    """
    print(f"=== 图像信息调试: {img_path} ===")
    
    try:
        # 使用skimage加载
        img_skimage = io.imread(img_path)
        print(f"skimage加载 - 形状: {img_skimage.shape}, 数据类型: {img_skimage.dtype}")
        print(f"skimage加载 - 值范围: [{img_skimage.min()}, {img_skimage.max()}]")
        
        # 使用PIL加载
        img_pil = Image.open(img_path)
        print(f"PIL加载 - 尺寸: {img_pil.size}, 模式: {img_pil.mode}")
        
        # 转换测试
        img_3c = convert_to_3channel(img_skimage)
        print(f"转换后 - 形状: {img_3c.shape}, 数据类型: {img_3c.dtype}")
        print(f"转换后 - 值范围: [{img_3c.min()}, {img_3c.max()}]")
        
    except Exception as e:
        print(f"调试过程中出错: {e}")
    
    print("=" * 50)


# 使用示例
if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        image_path = sys.argv[1]
        debug_image_info(image_path)
        
        try:
            img_tensor, original_shape, img_3c = load_and_preprocess_image(
                image_path, target_size=1024, device='cpu'
            )
            print(f"预处理成功！")
            print(f"输出tensor形状: {img_tensor.shape}")
            print(f"原始图像尺寸: {original_shape}")
            
        except Exception as e:
            print(f"预处理失败: {e}")
    else:
        print("用法: python image_preprocessing_utils.py <image_path>")
