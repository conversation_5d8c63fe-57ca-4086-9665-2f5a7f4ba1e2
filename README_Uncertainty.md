# MedSAM 不确定性估计

这个工具集用于MedSAM模型的不确定性估计，使用蒙特卡罗Dropout技术来量化模型预测的可靠性。

## 📁 文件说明

1. **`MedSAM_Uncertainty_Inference.py`** - 主要的不确定性推理脚本
2. **`run_uncertainty_test.py`** - 批处理和测试工具
3. **`README_Uncertainty.md`** - 使用说明文档

## 🚀 快速开始

### 方法1: 交互式使用（推荐新手）

```bash
python run_uncertainty_test.py --mode interactive
```

然后按提示输入：
- 图像路径
- 边界框坐标
- 模型权重路径
- 采样次数等参数

### 方法2: 单个图像测试

```bash
python MedSAM_Uncertainty_Inference.py \
    --data_path your_image.png \
    --box "[120,230,190,280]" \
    --checkpoint model.pth \
    --mc_samples 15 \
    --show_plot
```

### 方法3: 批量处理

```bash
python run_uncertainty_test.py \
    --mode batch \
    --image_dir /path/to/images/ \
    --box "[120,230,190,280]" \
    --mc_samples 10
```

## 📋 参数说明

### 主要参数

- `--data_path`: 输入图像路径
- `--box`: 边界框坐标，格式为 `"[x1,y1,x2,y2]"`
- `--checkpoint`: 模型权重文件路径
- `--mc_samples`: 蒙特卡罗采样次数（默认10，越大越准确但越慢）
- `--model_type`: 模型类型 (`vit_b`, `vit_l`, `vit_h`)
- `--device`: 计算设备 (`cuda:0`, `cpu`)
- `--output_dir`: 输出目录
- `--show_plot`: 显示可视化结果

### 高级参数

- `--mode`: 运行模式 (`single`, `batch`, `interactive`)
- `--image_dir`: 批量处理的图像目录

## 📊 输出结果

### 1. 可视化窗口
显示4个子图：
- **左上**: 原始图像 + 边界框
- **右上**: 分割结果
- **左下**: 不确定性热力图
- **右下**: 分割结果 + 不确定性叠加

### 2. 保存文件
- `segmentation_result.png`: 分割结果
- `uncertainty_heatmap.png`: 不确定性热力图

### 3. 控制台输出
- 不确定性统计信息（均值、标准差、最小值、最大值）
- 分割区域像素数
- 处理进度信息

## 🎯 不确定性解释

### 热力图颜色含义
- **红色/黄色**: 高不确定性区域，模型预测不太可靠
- **蓝色/黑色**: 低不确定性区域，模型预测比较可靠

### 不确定性数值
- **0.0**: 完全确定
- **1.0**: 完全不确定
- **0.1-0.3**: 低不确定性（可靠）
- **0.3-0.7**: 中等不确定性（需要注意）
- **0.7-1.0**: 高不确定性（不可靠）

## 💡 使用建议

### 1. 采样次数选择
- **快速测试**: 5-10次采样
- **一般使用**: 10-20次采样
- **高精度**: 20-50次采样
- **研究用途**: 50-100次采样

### 2. 边界框设置
确保边界框：
- 完全包含目标区域
- 不要过大（影响精度）
- 不要过小（遗漏边缘）

### 3. 结果解读
- 关注高不确定性区域
- 结合医学知识判断
- 可作为质量控制指标

## 🔧 故障排除

### 常见问题

**Q: 显示"CUDA out of memory"错误**
A: 减少`mc_samples`数量或使用CPU (`--device cpu`)

**Q: 不确定性热力图全是黑色**
A: 检查模型是否正确加载，或增加采样次数

**Q: 分割结果不准确**
A: 检查边界框坐标和模型权重路径

**Q: 处理速度很慢**
A: 减少采样次数或使用更快的GPU

### 调试模式

```bash
# 使用CPU进行调试
python MedSAM_Uncertainty_Inference.py \
    --data_path test_image.png \
    --device cpu \
    --mc_samples 5

# 检查模型加载
python -c "
import torch
checkpoint = torch.load('model.pth', map_location='cpu')
print('模型键:', list(checkpoint.keys()))
"
```

## 📈 性能优化

### 1. GPU优化
- 使用较新的GPU (RTX 3080+)
- 确保CUDA版本兼容
- 监控GPU内存使用

### 2. 参数调优
- 根据需求调整采样次数
- 使用合适的批处理大小
- 优化图像预处理

### 3. 批量处理
```bash
# 并行处理多个图像
for img in images/*.png; do
    python MedSAM_Uncertainty_Inference.py \
        --data_path "$img" \
        --output_dir "results/$(basename $img .png)" &
done
wait
```

## 🔬 技术原理

### 蒙特卡罗Dropout
1. **训练模式**: 在推理时保持Dropout层激活
2. **多次采样**: 对同一输入进行多次前向传播
3. **统计分析**: 计算多次预测结果的方差
4. **不确定性量化**: 方差反映预测的不确定性

### 实现细节
- 使用标准差作为不确定性度量
- 在图像编码器中应用Dropout
- 支持可视化和定量分析

## 📚 参考文献

1. Gal, Y., & Ghahramani, Z. (2016). Dropout as a bayesian approximation: Representing model uncertainty in deep learning.
2. Kendall, A., & Gal, Y. (2017). What uncertainties do we need in bayesian deep learning for computer vision?

## 🤝 贡献

欢迎提交问题和改进建议！

## 📄 许可证

本项目遵循原MedSAM项目的许可证。
