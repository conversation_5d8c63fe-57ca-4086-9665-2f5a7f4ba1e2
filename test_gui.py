import matplotlib
print(f"当前Matplotlib后端: {matplotlib.get_backend()}")

# 尝试使用不同的后端
backends_to_try = ['TkAgg', 'Qt5Agg', 'WXAgg', 'GTK3Agg','agg']

for backend in backends_to_try:
    try:
        print(f"尝试切换到 {backend} 后端...")
        matplotlib.use(backend, force=True)
        import matplotlib.pyplot as plt
        
        # 创建一个简单的图形
        plt.figure(figsize=(4, 3))
        plt.plot([1, 2, 3, 4], [1, 4, 9, 16])
        plt.title(f"使用 {backend} 后端的测试图形")
        
        # 显示图形
        plt.show()
        
        print(f"成功使用 {backend} 后端显示图形！")
        break
    except Exception as e:
        print(f"{backend} 后端失败: {str(e)}")