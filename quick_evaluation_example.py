#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速评估示例
针对MedSAM项目的分割结果评估
"""

import os
from enhanced_segmentation_evaluator import evaluate_segmentation
from simple_dice_calculator import calculate_segmentation_metrics


def quick_evaluate_medsam_results():
    """快速评估MedSAM分割结果"""
    
    print("=== MedSAM分割结果快速评估 ===")
    
    # 示例路径（请根据实际情况修改）
    base_path = "/media/ubun/Teacher/hyf/MedSAM/data/FLARE22Train"
    
    # 可能的文件夹组合
    folder_combinations = [
        {
            'name': 'FLARE22_33',
            'gt_folder': f"{base_path}/mask_33/",
            'pred_folder': f"{base_path}/results_33/",
            'output_file': f"{base_path}/evaluation_33.csv"
        },
        # 可以添加更多组合
        # {
        #     'name': 'FLARE22_34',
        #     'gt_folder': f"{base_path}/mask_34/",
        #     'pred_folder': f"{base_path}/results_34/",
        #     'output_file': f"{base_path}/evaluation_34.csv"
        # }
    ]
    
    # 检查哪些文件夹存在
    available_combinations = []
    for combo in folder_combinations:
        if os.path.exists(combo['gt_folder']) and os.path.exists(combo['pred_folder']):
            available_combinations.append(combo)
            print(f"✓ 找到数据集: {combo['name']}")
        else:
            print(f"✗ 数据集不存在: {combo['name']}")
            if not os.path.exists(combo['gt_folder']):
                print(f"    缺少真实标签文件夹: {combo['gt_folder']}")
            if not os.path.exists(combo['pred_folder']):
                print(f"    缺少预测结果文件夹: {combo['pred_folder']}")
    
    if not available_combinations:
        print("\n没有找到可用的数据集，请检查路径设置")
        return
    
    # 处理每个可用的数据集
    for combo in available_combinations:
        print(f"\n{'='*50}")
        print(f"正在评估: {combo['name']}")
        print(f"{'='*50}")
        
        try:
            # 使用增强版评估器
            results = evaluate_segmentation(
                combo['gt_folder'],
                combo['pred_folder'],
                combo['output_file'],
                create_plots=True,
                plot_dir=f"{base_path}/plots_{combo['name'].split('_')[-1]}"
            )
            
            if results is not None:
                print(f"✓ {combo['name']} 评估完成")
                print(f"  结果文件: {combo['output_file']}")
                
                # 显示关键统计信息
                key_metrics = ['Dice', 'IoU', 'Sensitivity', 'PPV']
                print(f"\n关键指标摘要:")
                for metric in key_metrics:
                    if metric in results.columns:
                        values = results[metric].dropna()
                        if len(values) > 0:
                            print(f"  {metric}: {values.mean():.4f} ± {values.std():.4f}")
            else:
                print(f"✗ {combo['name']} 评估失败")
                
        except Exception as e:
            print(f"✗ 评估 {combo['name']} 时出错: {str(e)}")


def custom_evaluation():
    """自定义评估"""
    print("\n=== 自定义评估 ===")
    
    # 获取用户输入
    gt_folder = input("请输入真实标签文件夹路径: ").strip().strip('"\'')
    pred_folder = input("请输入预测结果文件夹路径: ").strip().strip('"\'')
    
    if not os.path.exists(gt_folder):
        print(f"错误: 真实标签文件夹不存在 - {gt_folder}")
        return
    
    if not os.path.exists(pred_folder):
        print(f"错误: 预测结果文件夹不存在 - {pred_folder}")
        return
    
    # 输出文件设置
    output_file = input("请输入输出文件名（默认: custom_evaluation.csv）: ").strip()
    if not output_file:
        output_file = "custom_evaluation.csv"
    
    # 选择评估模式
    print("\n选择评估模式:")
    print("1. 完整评估（所有指标 + 可视化）")
    print("2. 快速评估（主要指标）")
    
    mode = input("请选择模式 (1/2, 默认: 1): ").strip()
    
    try:
        if mode == "2":
            # 快速评估
            print("\n使用快速评估模式...")
            results = calculate_segmentation_metrics(gt_folder, pred_folder, output_file)
        else:
            # 完整评估
            print("\n使用完整评估模式...")
            create_plots = input("是否创建可视化图表？(y/n, 默认: y): ").strip().lower() != 'n'
            
            results = evaluate_segmentation(
                gt_folder, pred_folder, output_file, 
                create_plots=create_plots
            )
        
        if results is not None:
            print(f"\n✓ 评估完成！结果已保存到: {output_file}")
        else:
            print(f"\n✗ 评估失败")
            
    except Exception as e:
        print(f"\n✗ 评估过程中出错: {str(e)}")


def batch_evaluation():
    """批量评估多个数据集"""
    print("\n=== 批量评估 ===")
    
    base_dir = input("请输入数据集根目录: ").strip().strip('"\'')
    
    if not os.path.exists(base_dir):
        print(f"错误: 目录不存在 - {base_dir}")
        return
    
    # 查找所有可能的数据集
    datasets = []
    for item in os.listdir(base_dir):
        if item.startswith('mask_'):
            dataset_id = item.split('_')[1]
            gt_folder = os.path.join(base_dir, f"mask_{dataset_id}")
            pred_folder = os.path.join(base_dir, f"results_{dataset_id}")
            
            if os.path.exists(pred_folder):
                datasets.append({
                    'id': dataset_id,
                    'gt_folder': gt_folder,
                    'pred_folder': pred_folder,
                    'output_file': os.path.join(base_dir, f"evaluation_{dataset_id}.csv")
                })
    
    if not datasets:
        print("没有找到匹配的数据集（mask_* 和 results_* 文件夹对）")
        return
    
    print(f"找到 {len(datasets)} 个数据集:")
    for ds in datasets:
        print(f"  - 数据集 {ds['id']}")
    
    confirm = input(f"\n是否继续批量评估这 {len(datasets)} 个数据集？(y/n): ").strip().lower()
    if confirm != 'y':
        print("已取消批量评估")
        return
    
    # 执行批量评估
    successful_count = 0
    for i, ds in enumerate(datasets, 1):
        print(f"\n[{i}/{len(datasets)}] 评估数据集 {ds['id']}...")
        
        try:
            results = evaluate_segmentation(
                ds['gt_folder'], ds['pred_folder'], ds['output_file'],
                create_plots=False  # 批量模式下不创建图表以节省时间
            )
            
            if results is not None:
                successful_count += 1
                print(f"✓ 数据集 {ds['id']} 评估完成")
            else:
                print(f"✗ 数据集 {ds['id']} 评估失败")
                
        except Exception as e:
            print(f"✗ 数据集 {ds['id']} 评估出错: {str(e)}")
    
    print(f"\n批量评估完成: {successful_count}/{len(datasets)} 个数据集成功")


def main():
    """主函数"""
    print("MedSAM分割结果评估工具")
    print("=" * 40)
    
    while True:
        print("\n请选择操作:")
        print("1. 快速评估（使用预设路径）")
        print("2. 自定义评估")
        print("3. 批量评估")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            quick_evaluate_medsam_results()
        elif choice == "2":
            custom_evaluation()
        elif choice == "3":
            batch_evaluation()
        elif choice == "4":
            print("再见！")
            break
        else:
            print("无效选择，请重新输入")


if __name__ == "__main__":
    main()
