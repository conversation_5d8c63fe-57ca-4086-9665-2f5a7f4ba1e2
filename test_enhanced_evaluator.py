#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强版分割评估器
验证所有指标的计算是否正确
"""

import os
import numpy as np
from PIL import Image
import tempfile
import shutil
from pathlib import Path

# 导入评估器
from enhanced_segmentation_evaluator import compute_comprehensive_metrics, evaluate_segmentation


def test_metrics_calculation():
    """测试指标计算的正确性"""
    print("=== 测试指标计算 ===")
    
    # 测试用例
    test_cases = [
        {
            'name': '完全匹配',
            'gt': np.array([[1, 1, 0], [1, 1, 0], [0, 0, 0]], dtype=bool),
            'pred': np.array([[1, 1, 0], [1, 1, 0], [0, 0, 0]], dtype=bool),
            'expected': {
                'Dice': 1.0,
                'IoU': 1.0,
                'Sensitivity': 1.0,
                'PPV': 1.0,
                'Specificity': 1.0,
                'Accuracy': 1.0
            }
        },
        {
            'name': '完全不匹配',
            'gt': np.array([[1, 1, 0], [1, 1, 0], [0, 0, 0]], dtype=bool),
            'pred': np.array([[0, 0, 1], [0, 0, 1], [1, 1, 1]], dtype=bool),
            'expected': {
                'Dice': 0.0,
                'IoU': 0.0,
                'Sensitivity': 0.0,
                'PPV': 0.0,
                'Specificity': 0.0,
                'Accuracy': 1/9  # 只有1个TN
            }
        },
        {
            'name': '部分重叠',
            'gt': np.array([[1, 1, 0], [1, 1, 0], [0, 0, 0]], dtype=bool),
            'pred': np.array([[1, 0, 0], [1, 0, 0], [0, 0, 0]], dtype=bool),
            'expected': {
                'TP': 2,
                'FP': 0,
                'FN': 2,
                'TN': 5,
                'Dice': 2*2/(2*2+0+2),  # 4/6 = 0.667
                'IoU': 2/(2+0+2),       # 2/4 = 0.5
                'Sensitivity': 2/(2+2), # 2/4 = 0.5
                'PPV': 2/(2+0),         # 2/2 = 1.0
                'Specificity': 5/(5+0), # 5/5 = 1.0
                'Accuracy': (2+5)/9     # 7/9 = 0.778
            }
        }
    ]
    
    for case in test_cases:
        print(f"\n测试用例: {case['name']}")
        
        metrics = compute_comprehensive_metrics(case['gt'], case['pred'])
        
        # 验证期望的指标
        for key, expected_value in case['expected'].items():
            calculated_value = metrics[key]
            
            if np.isnan(expected_value):
                assert np.isnan(calculated_value), f"{key}: 期望NaN，得到{calculated_value}"
            else:
                assert abs(calculated_value - expected_value) < 0.001, \
                    f"{key}: 期望{expected_value:.4f}，得到{calculated_value:.4f}"
            
            print(f"  {key}: {calculated_value:.4f} ✓")
    
    print("\n所有指标计算测试通过！")


def create_test_dataset():
    """创建测试数据集"""
    temp_dir = tempfile.mkdtemp()
    folder1 = os.path.join(temp_dir, "ground_truth")
    folder2 = os.path.join(temp_dir, "predictions")
    
    os.makedirs(folder1, exist_ok=True)
    os.makedirs(folder2, exist_ok=True)
    
    # 创建不同质量的测试图像
    test_images = [
        {
            'name': 'high_quality',
            'gt': np.random.rand(50, 50) > 0.7,
            'pred_noise': 0.05  # 5%噪声
        },
        {
            'name': 'medium_quality',
            'gt': np.random.rand(50, 50) > 0.6,
            'pred_noise': 0.15  # 15%噪声
        },
        {
            'name': 'low_quality',
            'gt': np.random.rand(50, 50) > 0.5,
            'pred_noise': 0.3   # 30%噪声
        },
        {
            'name': 'empty_gt',
            'gt': np.zeros((50, 50), dtype=bool),
            'pred_noise': 0.1
        },
        {
            'name': 'full_gt',
            'gt': np.ones((50, 50), dtype=bool),
            'pred_noise': 0.1
        }
    ]
    
    for img_info in test_images:
        gt_mask = img_info['gt'].astype(np.uint8) * 255
        
        # 创建带噪声的预测结果
        pred_mask = img_info['gt'].copy()
        noise_mask = np.random.rand(*pred_mask.shape) < img_info['pred_noise']
        pred_mask = pred_mask ^ noise_mask  # XOR操作添加噪声
        pred_mask = pred_mask.astype(np.uint8) * 255
        
        # 保存图像
        Image.fromarray(gt_mask, mode='L').save(
            os.path.join(folder1, f"{img_info['name']}.png"))
        Image.fromarray(pred_mask, mode='L').save(
            os.path.join(folder2, f"{img_info['name']}.png"))
    
    return folder1, folder2, temp_dir


def test_batch_evaluation():
    """测试批量评估功能"""
    print("\n=== 测试批量评估 ===")
    
    # 创建测试数据
    folder1, folder2, temp_dir = create_test_dataset()
    
    try:
        output_file = os.path.join(temp_dir, "test_evaluation.csv")
        
        # 执行评估（不创建图表以加快测试）
        results = evaluate_segmentation(
            folder1, folder2, output_file, 
            create_plots=False
        )
        
        # 验证结果
        assert results is not None, "评估结果为空"
        assert len(results) == 5, f"期望5个结果，得到{len(results)}"
        
        # 检查必要的列是否存在
        required_columns = ['Filename', 'Dice', 'IoU', 'Sensitivity', 'PPV']
        for col in required_columns:
            assert col in results.columns, f"缺少列: {col}"
        
        # 检查CSV文件是否创建
        assert os.path.exists(output_file), "CSV文件未创建"
        
        # 检查统计文件是否创建
        stats_file = output_file.replace('.csv', '_statistics.csv')
        assert os.path.exists(stats_file), "统计文件未创建"
        
        print(f"✓ 成功处理{len(results)}个文件对")
        print(f"✓ 输出文件创建成功")
        print(f"✓ 统计文件创建成功")
        
        # 显示一些结果
        print(f"\n样本结果:")
        display_cols = ['Filename', 'Dice', 'IoU', 'Sensitivity', 'PPV']
        print(results[display_cols].head(3).to_string(index=False, float_format='%.4f'))
        
    finally:
        # 清理临时文件
        shutil.rmtree(temp_dir)
        print(f"\n已清理临时文件: {temp_dir}")


def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")
    
    # 空掩码
    empty_gt = np.zeros((10, 10), dtype=bool)
    empty_pred = np.zeros((10, 10), dtype=bool)
    metrics = compute_comprehensive_metrics(empty_gt, empty_pred)
    print(f"空掩码 - Dice: {metrics['Dice']:.4f}, IoU: {metrics['IoU']:.4f}")
    
    # 全掩码
    full_gt = np.ones((10, 10), dtype=bool)
    full_pred = np.ones((10, 10), dtype=bool)
    metrics = compute_comprehensive_metrics(full_gt, full_pred)
    print(f"全掩码 - Dice: {metrics['Dice']:.4f}, IoU: {metrics['IoU']:.4f}")
    
    # GT为空，预测非空
    empty_gt = np.zeros((10, 10), dtype=bool)
    nonempty_pred = np.ones((10, 10), dtype=bool)
    metrics = compute_comprehensive_metrics(empty_gt, nonempty_pred)
    print(f"GT空，预测非空 - Dice: {metrics['Dice']:.4f}, Sensitivity: {metrics['Sensitivity']}")
    
    # GT非空，预测为空
    nonempty_gt = np.ones((10, 10), dtype=bool)
    empty_pred = np.zeros((10, 10), dtype=bool)
    metrics = compute_comprehensive_metrics(nonempty_gt, empty_pred)
    print(f"GT非空，预测空 - Dice: {metrics['Dice']:.4f}, PPV: {metrics['PPV']}")
    
    print("✓ 边界情况测试通过")


def run_all_tests():
    """运行所有测试"""
    print("开始运行增强版分割评估器测试...")
    
    try:
        test_metrics_calculation()
        test_edge_cases()
        test_batch_evaluation()
        
        print(f"\n🎉 所有测试都通过了!")
        print(f"\n增强版分割评估器功能验证完成，支持的指标包括：")
        print(f"- Dice系数、IoU、Sensitivity、PPV")
        print(f"- Specificity、NPV、Accuracy、F1-Score")
        print(f"- Balanced Accuracy、MCC、Volume Similarity")
        print(f"- 混淆矩阵（TP、FP、FN、TN）")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        raise


if __name__ == "__main__":
    run_all_tests()
